from flask import Flask, render_template, send_file
import pandas as pd
import openpyxl
import os
import requests
from datetime import datetime
from openpyxl.styles import Border, Side


def getreport(date):
    # replace localhost with real IP Address
    url = "http://***********:8104/melting/daily-report"
    params = {
        'date': date,
        'output': 'json'
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        json_data = response.json()
        return json_data
        

    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")

# Get Recup Json Data
def getrecup(b,e):
    url = "http://***********:8104/melting/recap"
    params = {
        'from': b,
        'to': e,
        'output': 'json'
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        json_data = response.json()
        return json_data
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")

app = Flask(__name__)

SOURCE_FILE = 'dailyTemplate.xlsx'

@app.route('/')
def home():
    return render_template("index.html")

@app.route('/report/<date>')
def download(date):
    # delete file dailyreport.xlsx if any
    if os.path.exists('dailyReport.xlsx'):
        os.remove('dailyReport.xlsx')
    #copy dailytemplate
    new_file = openpyxl.load_workbook(SOURCE_FILE)
    sheet = new_file.active
    # current_date = datetime.now().strftime("%d/%m/%Y")
    # fill cell B2 with current date
    sheet.cell(row=2, column=2).value = date
    NOMOR = 1
    data = getreport(date)
    if isinstance(data, list):
        df = pd.DataFrame(data)
        # print(df)
        METAL = df['metal'].tolist()
        COLOR = df['color'].tolist()
        ORIGINAL = df['original_weight'].tolist()
        ALLOY = df['alloy_weight'].tolist()
        POTONGAN = df['potongan_weight'].tolist()
        POHON = df['pohon_weight'].tolist()
        IN_JUJO = df['melting_weight'].tolist()
        OUT_JUJO = df['box_weight'].tolist()
        GRANULE = df['granule_weight'].tolist()
        # LOSS = (df['loss_weight']/100).tolist()
        L24K = df['k24k_weight'].tolist()
        KADAR_KARAT = df['kadarkarat'].tolist()
        t_ori = 0
        t_all = 0
        t_poh = 0
        t_pot = 0
        t_mel = 0
        t_box = 0
        t_gra = 0
        # t_los = 0
        t_k24 = 0
        t_g24 = 0
        for i in range(len(METAL)):
            sheet.cell(row=i+4, column=1).value = NOMOR
            sheet.cell(row=i+4, column=2).value = METAL[i]
            sheet.cell(row=i+4, column=3).value = COLOR[i]
            sheet.cell(row=i+4, column=4).value = ORIGINAL[i]
            sheet.cell(row=i+4, column=5).value = ALLOY[i]
            sheet.cell(row=i+4, column=6).value = POHON[i]
            sheet.cell(row=i+4, column=7).value = POTONGAN[i]
            sheet.cell(row=i+4, column=8).value = IN_JUJO[i]
            sheet.cell(row=i+4, column=9).value = OUT_JUJO[i]
            # sheet.cell(row=i+4, column=11).value = LOSS[i]
            loss = (IN_JUJO[i] - OUT_JUJO[i])
            sheet.cell(row=i+4, column=10).value = loss
            # sheet.cell(row=i+4, column=11).value = LOSS[i]
            k24k = (loss * KADAR_KARAT[i])
            sheet.cell(row=i+4, column=11).value = k24k
            G24K = (GRANULE[i] * KADAR_KARAT[i])
            LOSS_RATE = (loss/IN_JUJO[i])
            # sheet.cell(row=i+4, column=13).value = G24K
            sheet.cell(row=i+4, column=12).value = LOSS_RATE
            sheet.cell(row=i+4, column=13).value = GRANULE[i]
            sheet.cell(row=i+4, column=14).value = KADAR_KARAT[i]
            NOMOR += 1
            t_ori += ORIGINAL[i]
            t_all += ALLOY[i]
            t_poh += POHON[i]
            t_pot += POTONGAN[i]
            t_mel += IN_JUJO[i]
            t_box += OUT_JUJO[i]
            t_gra += GRANULE[i]
            # t_los += LOSS[i]
            # t_los += loss
            t_k24 += k24k
            t_g24 += G24K
        
        sheet.cell(row=len(METAL)+4, column=1).value = "Total"
        sheet.cell(row=len(METAL)+4, column=4).value = t_ori
        sheet.cell(row=len(METAL)+4, column=5).value = t_all
        sheet.cell(row=len(METAL)+4, column=7).value = t_poh
        sheet.cell(row=len(METAL)+4, column=6).value = t_pot
        sheet.cell(row=len(METAL)+4, column=8).value = t_mel
        sheet.cell(row=len(METAL)+4, column=9).value = t_box
        # sheet.cell(row=len(METAL)+4, column=11).value = t_los
        sheet.cell(row=len(METAL)+4, column=10).value = (t_mel - t_box)
        sheet.cell(row=len(METAL)+4, column=11).value = t_k24
        # sheet.cell(row=len(METAL)+4, column=13).value = t_g24
        lossrate = (t_mel - t_box) / t_mel
        sheet.cell(row=len(METAL)+4, column=12).value = lossrate
        sheet.cell(row=len(METAL)+4, column=13).value = t_gra
        sheet.merge_cells(start_row=len(METAL)+4, start_column=1, end_row=len(METAL)+4, end_column=3)
        
        # Total Plus Granule
        sheet.cell(row=len(METAL)+5, column=1).value = "Total+Granule"
        sheet.cell(row=len(METAL)+5, column=4).value = t_ori
        sheet.cell(row=len(METAL)+5, column=5).value = t_all
        sheet.cell(row=len(METAL)+5, column=7).value = t_poh
        sheet.cell(row=len(METAL)+5, column=6).value = t_pot
        sheet.cell(row=len(METAL)+5, column=8).value = t_mel
        sheet.cell(row=len(METAL)+5, column=9).value = (t_box + t_gra)
        sheet.cell(row=len(METAL)+5, column=10).value = (t_mel - (t_box + t_gra))
        sheet.cell(row=len(METAL)+5, column=11).value = (t_k24 - t_g24)
        # sheet.cell(row=len(METAL)+5, column=13).value = t_g24
        lossrate = (t_mel - (t_box+t_gra)) / t_mel
        sheet.cell(row=len(METAL)+5, column=12).value = lossrate
        sheet.cell(row=len(METAL)+5, column=13).value = t_gra
        sheet.merge_cells(start_row=len(METAL)+5, start_column=1, end_row=len(METAL)+5, end_column=3)

        # Fill lates two rows with 00a933 color
        row_beg = len(METAL)+4
        row_end = len(METAL)+5
        col_beg = 1
        col_end = 14
        for row in range(row_beg, row_end + 1):
            for col in range(col_beg, col_end + 1):
                sheet.cell(row=row, column=col).fill = openpyxl.styles.PatternFill(start_color='00a65a', end_color='00a65a', fill_type='solid')
                sheet.cell(row=row, column=col).font = openpyxl.styles.Font(color='FFFFFF', bold=True)
       
       # Delete rows start from row_beg + 2 until row 30
        sheet.delete_rows(row_beg + 2, 30 - (row_beg + 1))

        new_file.save('dailyReport.xlsx')

        return send_file('dailyReport.xlsx', as_attachment=True)
    else:
        return "Unexpected JSON format. Expected a list of dictionaries.", 500

@app.route('/recup/<from_date>/<to_date>')
def recup(from_date, to_date):
    # prepare files
    if os.path.exists('meltingrecap.xlsx'):
        os.remove('meltingrecap.xlsx')
    # copy recupTemplate
    new_file = openpyxl.load_workbook('recupTemplate.xlsx')
    # sheet = new_file.active
    sheet = 'Lengkap'
    sheet.cell(row=2, column=3).value = from_date
    sheet.cell(row=3, column=3).value = to_date

    data = getrecup(from_date, to_date)
    if isinstance(data, list):
        df = pd.DataFrame(data)
        # print(df)
        # Put df to meltingrecap.xlsx
        # melting_date metal_color  in_weight  out_weight  total_granule
        tanggal = df['melting_date'].tolist()
        metacol = df['metal_color'].tolist()
        iweigth = df['in_weight'].tolist()
        oweight = df['out_weight'].tolist()
        granule = df['total_granule'].tolist()
        last_row=0;
        nomor = 1;
        for i in range(len(tanggal)):
            sheet.cell(row=i+6, column=1).value = nomor
            sheet.cell(row=i+6, column=2).value = tanggal[i]
            sheet.cell(row=i+6, column=3).value = metacol[i]
            sheet.cell(row=i+6, column=4).value = iweigth[i]
            sheet.cell(row=i+6, column=5).value = oweight[i]
            sheet.cell(row=i+6, column=6).value = granule[i]
            last_row = i+6
            nomor += 1

        # remove border from last row to next two rows
        for brow in sheet.iter_rows(min_row=last_row+1, max_row=last_row+2, min_col=1, max_col=6):
            for cell in brow:
                cell.border=Border()

        summary = df.groupby("metal_color").agg(
                total_in_weight=('in_weight', 'sum'),
                total_out_weight=('out_weight', 'sum'),
                total_granule=('total_granule', 'sum')
            ).reset_index()
        
        sheet.cell(row=last_row+3, column=3).value = "Metal-Color"
        sheet.cell(row=last_row+3, column=4).value = "Total In Weight"
        sheet.cell(row=last_row+3, column=5).value = "Total Out Weight"
        sheet.cell(row=last_row+3, column=6).value = "Total Granule"

        lrow = 0;
        for i in range(len(summary)):
            sheet.cell(row=i+last_row+4, column=3).value = summary.iloc[i]['metal_color']
            sheet.cell(row=i+last_row+4, column=4).value = summary.iloc[i]['total_in_weight']
            sheet.cell(row=i+last_row+4, column=5).value = summary.iloc[i]['total_out_weight']
            sheet.cell(row=i+last_row+4, column=6).value = summary.iloc[i]['total_granule']
            lrow = i+last_row+4
        
        # delete row from last_row+4 until row 54
        sheet.delete_rows(lrow+1, 54 - (last_row+4))

        new_file.save('meltingrecap.xlsx')
        return send_file('meltingrecap.xlsx', as_attachment=True)
        
        # table_html = df.to_html(classes='table table-bordered', index=False)
        # table_resume = summary.to_html(classes='table table-bordered', index=False)
        # return render_template('index.html', table=table_html, resume=table_resume)


    else:
        return "Unexpected JSON format. Expected a list of dictionaries.", 500
    
    return render_template("index.html")

@app.route('/test')
def test():
    data = getreport('2024-12-07')
    if isinstance(data, list):
        df = pd.DataFrame(data)
        # print(df)
        
    
    else:
        return (f"Error: 'data' is not a list. Actual type: {type(data)}") 
        # return "Unexpected JSON format. Expected a list of dictionaries.", 500
    # return render_template("index.html")
    
    

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
    # app.run(debug=True)