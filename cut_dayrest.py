import requests
import os
from openpyxl.styles import Alignment
from openpyxl import load_workbook
from datetime import datetime

def getdata():
    # get json data from http://qcstag.veronique.idn/api/cutting/dailyStock
    url = "http://qcstag.veronique.idn/api/cutting/dailyStock"
    params = {
        'output': 'json'
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        json_data = response.json()
        return json_data
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return None
    
def opentempreport():
    # open file /templates/cut_dayrest.xlsx
    template_path = os.path.join('templates', 'cut_dayrest.xlsx')

    # load workbook
    wb = load_workbook(template_path)
    return wb


def filldata(wb):
    output_path = os.path.join('templates', 'cutting_dailyrest.xlsx')
    data = getdata()
    # get current date from os system
    today = datetime.now().strftime("%d/%m/%Y")
    sheet = wb.active
    # Alignment configuration
    ca = Alignment(horizontal='center', vertical='center')
    la = Alignment(horizontal='left', vertical='center')
    ra = Alignment(horizontal='right', vertical='center')

    # set today to Cell C1
    sheet['C1'] = today

    # Column indexes = Category	Metal-Color	Thong Number	Barcode	Item Name	Qty	Weight
    # start from row 4
    row_num = 4

    # total qty and total wgt init
    t_qty = 0
    t_wgt = 0

    # loop data.order
    if 'order' in data:
        for row in data['order']:
            sheet[f'A{row_num}'] = row.get('category', '')
            sheet[f'A{row_num}'].alignment = la
            sheet[f'B{row_num}'] = row.get('metal_color', '')
            sheet[f'B{row_num}'].alignment = la
            sheet[f'C{row_num}'] = row.get('thong_number', '')
            sheet[f'C{row_num}'].alignment = la
            sheet[f'D{row_num}'] = row.get('barcode', '')
            sheet[f'D{row_num}'].alignment = la
            sheet[f'E{row_num}'] = row.get('item_name', '')
            sheet[f'E{row_num}'].alignment = la
            sheet[f'F{row_num}'] = row.get('stock_qty', '')
            sheet[f'F{row_num}'].alignment = ra
            sheet[f'G{row_num}'] = row.get('stock_wgt', '')
            sheet[f'G{row_num}'].alignment = ra

            # total qty and total wgt
            t_qty += int(row.get('stock_qty', 0))
            t_wgt += float(row.get('stock_wgt', 0))

            row_num += 1
    
    # check isset data.recasting
    if 'recasting' in data:
        for row in data['recasting']:
            sheet[f'A{row_num}'] = row.get('category', '')
            sheet[f'A{row_num}'].alignment = la
            sheet[f'B{row_num}'] = row.get('metal_color', '')
            sheet[f'B{row_num}'].alignment = la
            sheet[f'C{row_num}'] = row.get('thong_number', '')
            sheet[f'C{row_num}'].alignment = la
            sheet[f'D{row_num}'] = row.get('barcode', '')
            sheet[f'D{row_num}'].alignment = la
            sheet[f'E{row_num}'] = row.get('item_name', '')
            sheet[f'E{row_num}'].alignment = la
            sheet[f'F{row_num}'] = row.get('stock_qty', '')
            sheet[f'F{row_num}'].alignment = ra
            sheet[f'G{row_num}'] = row.get('stock_wgt', '')
            sheet[f'G{row_num}'].alignment = ra
            
            # total qty and total wgt
            t_qty += int(row.get('stock_qty', 0))
            t_wgt += float(row.get('stock_wgt', 0))
            
            row_num += 1
    
    if 'two_tones' in data:
        for row in data['two_tones']:
            sheet[f'A{row_num}'] = row.get('category', '')
            sheet[f'A{row_num}'].alignment = la
            sheet[f'B{row_num}'] = row.get('metal_color', '')
            sheet[f'B{row_num}'].alignment = la
            sheet[f'C{row_num}'] = row.get('thong_number', '')
            sheet[f'C{row_num}'].alignment = la
            sheet[f'D{row_num}'] = row.get('barcode', '')
            sheet[f'D{row_num}'].alignment = la
            sheet[f'E{row_num}'] = row.get('item_name', '')
            sheet[f'E{row_num}'].alignment = la
            sheet[f'F{row_num}'] = row.get('stock_qty', '')
            sheet[f'F{row_num}'].alignment = ra
            sheet[f'G{row_num}'] = row.get('stock_wgt', '')
            sheet[f'G{row_num}'].alignment = ra
            
            # total qty and total wgt
            t_qty += int(row.get('stock_qty', 0))
            t_wgt += float(row.get('stock_wgt', 0))
            
            row_num += 1
    
    if 'tritones' in data:
        for row in data['tritones']:
            sheet[f'A{row_num}'] = row.get('category', '')
            sheet[f'A{row_num}'].alignment = la
            sheet[f'B{row_num}'] = row.get('metal_color', '')
            sheet[f'B{row_num}'].alignment = la
            sheet[f'C{row_num}'] = row.get('thong_number', '')
            sheet[f'C{row_num}'].alignment = la
            sheet[f'D{row_num}'] = row.get('barcode', '')
            sheet[f'D{row_num}'].alignment = la
            sheet[f'E{row_num}'] = row.get('item_name', '')
            sheet[f'E{row_num}'].alignment = la
            sheet[f'F{row_num}'] = row.get('stock_qty', '')
            sheet[f'F{row_num}'].alignment = ra
            sheet[f'G{row_num}'] = row.get('stock_wgt', '')
            sheet[f'G{row_num}'].alignment = ra
            
            # total qty and total wgt
            t_qty += int(row.get('stock_qty', 0))
            t_wgt += float(row.get('stock_wgt', 0))
            
            row_num += 1

    # Fill total qty and total wgt
    sheet[f'F{row_num}'] = t_qty
    sheet[f'F{row_num}'].alignment = ra
    sheet[f'G{row_num}'] = t_wgt
    sheet[f'G{row_num}'].alignment = ra

    wb.save(output_path)
    return output_path



