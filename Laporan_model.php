<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Laporan_model extends CI_Model
{

    public $table = 'tb_proses_order_detail';
    public $id = 'id_proses_detail';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // datatables
    function json($bagian=5) {
        $this->datatables->select('a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,c.item_number,a.barcode,a.metal,a.qty,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian');
        $this->datatables->from('tb_proses_order_detail a');
        //add this line for join
        //$this->datatables->join('table2', 'tb_proses_order_detail.field = table2.field');
        $this->datatables->join('master_bagian b', 'b.id_bagian=a.id_bagian_pengirim', 'left');
        $this->datatables->join('master_item c', 'c.id_item  = a.item_id', 'left');
        $this->datatables->join('tb_proses_order f', 'f.id_proses  = a.proses_order_id', 'left');
         $filter = $this->session->userdata('filter');

         if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->datatables->where('f.melting_type_id', $filter['m']);
        }
        
        if(isset($filter['po']) &&  ($filter['po'] !=''  ) ){
             $this->datatables->where('a.no_po', $filter['po']);
                if($filter['s'] != 'in'){
                $this->datatables->where('(a.status = \'finish\' OR a.status = \'finish repair\')');
            }

        }else{
            if(isset($filter['s']) &&  $filter['s'] !='' ){
                if($filter['s'] == 'in'){
                    //$this->datatables->where("status = 'proses'");
                    if(isset($filter['d']) &&  $filter['d'] !='' ){
                         $this->datatables->where('DATE(waktu_in) = \''.$filter['d'].'\'');
                    }

                }else{
                    //$this->datatables->where(" (status = 'finish' OR status='sending') ");
                    if(isset($filter['d']) &&  $filter['d'] !='' ){
                        $this->datatables->where('(a.status = \'finish\' OR a.status = \'finish repair\')');
                        $this->datatables->where('DATE(waktu_out) = \''.$filter['d'].'\'');
                    }

                }
               //
            }


        }
        

        if(isset($filter['b']) &&  $filter['b'] !='' ){
             $this->datatables->where('a.id_bagian_asal', $filter['b']);
        }else{
             $this->datatables->where('a.id_bagian_asal', $bagian);
        }
        
     //    $this->datatables->where("a.status = 'finish' or a.status = 'finish repair' ");
        $this->datatables->add_column('action', anchor(site_url('laporan/read/$1'),'<small class="label bg-green"><i class="fa fa-search"></i></small>')."  ".anchor(site_url('laporan/update/$1'),'<small class="label bg-yellow"><i class="fa fa-pencil"></i></small>')."  ".anchor(site_url('laporan/delete/$1'),'<small class="label bg-red"><i class="fa fa-trash-o"></i></small>','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'id_proses_detail');
        return $this->datatables->generate();
    }


   function json_loss_bagian($bagian=5) {
        $tgl = date('Y-m-d');
        $this->datatables->select('a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,e.item_number,a.barcode,a.metal,a.qty,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian, c.nama_bagian as pengirim, d.nama_bagian as tujuan,');
        $this->datatables->from('tb_proses_order_detail a');
        //add this line for join
        //$this->datatables->join('table2', 'tb_proses_order_detail.field = table2.field');
        $this->datatables->join('master_bagian b', 'b.id_bagian=a.id_bagian_asal', 'left');
        $this->datatables->join('master_bagian c', 'c.id_bagian=a.id_bagian_pengirim', 'left');
        $this->datatables->join('master_bagian d', 'd.id_bagian=a.id_bagian_tujuan', 'left');
          $this->datatables->join('tb_proses_order f', 'f.id_proses  = a.proses_order_id', 'left');
         $this->datatables->join('master_item e', 'e.id_item  = a.item_id', 'left');
         $filter = $this->session->userdata('filter');
        
        // if(isset($filter['b']) &&  $filter['b'] !='' ){
        //     $this->datatables->where("a.id_bagian_asal = '".$filter['b']."' AND (a.status = 'finish' OR a.status = 'finish repair'  OR a.status = 'sending repair'  OR a.status = 'sending') ");
        // }else{
        //     $this->datatables->where("a.id_bagian_asal = '".$bagian."' AND (a.status = 'finish' OR a.status = 'finish repair'  OR a.status = 'sending repair'  OR a.status = 'sending') ");
        // }
        
        if(isset($filter['b']) &&  $filter['b'] !=''  && $filter['b'] !='all' ){
            $this->datatables->where("a.id_bagian_asal = '".$filter['b']."' AND (a.status = 'finish' OR a.status = 'finish repair') ");
        }else{
            $this->datatables->where("(a.status = 'finish' OR a.status = 'finish repair') ");
        }
        

       if(isset($filter['d']) &&  $filter['d'] !='' ){
             $this->datatables->where('DATE(a.waktu_out) = \''.$filter['d'].'\'');
        }else{
            $this->datatables->where('DATE(a.waktu_out) = \''.$tgl.'\'');
        }

        if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->datatables->where('f.melting_type_id', $filter['m']);
        }
        
        $this->datatables->add_column('action', anchor(site_url('laporan/read/$1'),'<small class="label bg-green"><i class="fa fa-search"></i></small>')."  ".anchor(site_url('laporan/update/$1'),'<small class="label bg-yellow"><i class="fa fa-pencil"></i></small>')."  ".anchor(site_url('laporan/delete/$1'),'<small class="label bg-red"><i class="fa fa-trash-o"></i></small>','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'id_proses_detail');
        return $this->datatables->generate();
    }



   function json_iventory($bagian=5) {
        $tgl = date('Y-m-d');
        $this->datatables->select('a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,e.item_number,a.barcode,a.metal,a.qty,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian, c.nama_bagian as pengirim, d.nama_bagian as tujuan,');
        $this->datatables->from('tb_proses_order_detail a');
        $this->datatables->join('master_bagian b', 'b.id_bagian=a.id_bagian_asal', 'left');
        $this->datatables->join('master_bagian c', 'c.id_bagian=a.id_bagian_pengirim', 'left');
        $this->datatables->join('master_bagian d', 'd.id_bagian=a.id_bagian_tujuan', 'left');
          $this->datatables->join('tb_proses_order f', 'f.id_proses  = a.proses_order_id', 'left');
         $this->datatables->join('master_item e', 'e.id_item  = a.item_id', 'left');
         $filter = $this->session->userdata('filter');
        
        if(isset($filter['b']) &&  $filter['b'] !='' && $filter['b'] !='all'  ){
            $this->datatables->where("a.id_bagian_asal = '".$filter['b']."' AND (a.status = 'finish' OR a.status = 'finish repair') ");
        }else{
            $this->datatables->where("a.id_bagian_asal = '".$bagian."' AND (a.status = 'finish' OR a.status = 'finish repair') ");
        }
        

       if(isset($filter['d']) &&  $filter['d'] !='' ){
             $this->datatables->where('DATE(a.waktu_out) = \''.$filter['d'].'\'');
        }

        if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->datatables->where('f.melting_type_id', $filter['m']);
        }
        
        $this->datatables->add_column('action', anchor(site_url('laporan/read/$1'),'<small class="label bg-green"><i class="fa fa-search"></i></small>')."  ".anchor(site_url('laporan/update/$1'),'<small class="label bg-yellow"><i class="fa fa-pencil"></i></small>')."  ".anchor(site_url('laporan/delete/$1'),'<small class="label bg-red"><i class="fa fa-trash-o"></i></small>','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'id_proses_detail');
        return $this->datatables->generate();
    }


    function json_item_bak($bagian=5) {
        $tgl = date('Y-m-d');
        $this->datatables->select('a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,e.item_number,a.barcode,a.metal,a.qty,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian, c.nama_bagian as pengirim, d.nama_bagian as tujuan,f.melting_type_id');
        $this->datatables->from('tb_proses_order_detail a');
        //add this line for join
        //$this->datatables->join('table2', 'tb_proses_order_detail.field = table2.field');
        $this->datatables->join('master_bagian b', 'b.id_bagian=a.id_bagian_asal', 'left');
        $this->datatables->join('master_bagian c', 'c.id_bagian=a.id_bagian_pengirim', 'left');
        $this->datatables->join('master_bagian d', 'd.id_bagian=a.id_bagian_tujuan', 'left');
        $this->datatables->join('master_item e', 'e.id_item  = a.item_id', 'left');
        $this->datatables->join('tb_proses_order f', 'f.id_proses  = a.proses_order_id', 'left');
         $filter = $this->session->userdata('filter');
       
        if(isset($filter['b']) &&  $filter['b'] !='' ){
             $this->datatables->where('a.id_bagian_asal', $filter['b']);
        }else{
             $this->datatables->where('a.id_bagian_asal', $bagian);
        }
        
         if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->datatables->where('f.melting_type_id', $filter['m']);
        }
        
        if(isset($filter['p']) &&  $filter['p'] !='0'  ){
            $this->datatables->where(' (DATE(a.waktu_in) <= \''.$filter['d'].'\' AND (DATE(a.waktu_out) >= \''.$filter['d'].'\' OR a.waktu_out = NULL) )');
        }else{
            $this->datatables->where("a.status != 'finish' AND a.status != 'finish repair' ");
        }
        


    

        $this->datatables->add_column('action', anchor(site_url('laporan/read/$1'),'<small class="label bg-green"><i class="fa fa-search"></i></small>')."  ".anchor(site_url('laporan/update/$1'),'<small class="label bg-yellow"><i class="fa fa-pencil"></i></small>')."  ".anchor(site_url('laporan/delete/$1'),'<small class="label bg-red"><i class="fa fa-trash-o"></i></small>','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'id_proses_detail');
        return $this->datatables->generate();
    }


    function json_item($bagian=5) {
        $tgl = date('Y-m-d');
        $filter = $this->session->userdata('filter');
       if(isset($filter['d']) &&  $filter['d'] !='' ){
            $date = date('Y-m-d', strtotime($filter['d'] . ' +1 day')); 
            $where = 'WHERE input_date <  \''.$date.'\' ';
        }else{
            $where = '';
        }
        $this->datatables->select('a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,e.item_number,a.barcode,a.metal,a.qty,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian, c.nama_bagian as pengirim, d.nama_bagian as tujuan,g.melting_type_id,h.title_melting_type,i.size,i.nilai,f.*');
        $this->datatables->from('tb_proses_order_detail a');
        //add this line for join
        //$this->datatables->join('table2', 'tb_proses_order_detail.field = table2.field');
        $this->datatables->join('master_bagian b', 'b.id_bagian=a.id_bagian_asal', 'left');
        $this->datatables->join('master_bagian c', 'c.id_bagian=a.id_bagian_pengirim', 'left');
        $this->datatables->join('master_bagian d', 'd.id_bagian=a.id_bagian_tujuan', 'left');
        $this->datatables->join('master_item e', 'e.id_item  = a.item_id', 'left');
      //  $this->db->join('tb_weight_proses f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');
        $this->datatables->join('tb_proses_order g', 'g.id_proses  = a.proses_order_id', 'left');
        $this->datatables->join('master_melting_type h', 'h.id_melting_type  = g.melting_type_id', 'left');
        $this->datatables->join('master_size i', 'i.id  = h.size_id', 'left');
                $this->datatables->join('(SELECT
                            sum( gold_part ) AS out_gold_part,
                            sum( gold_chain ) AS out_gold_chain,
                            sum( other_part ) AS out_other_part,
                            sum( stone_weight ) AS out_stone_weight,
                            input_date,
                            proses_order_detail_id 
                        FROM
                            tb_weight_proses '.$where.'  GROUP BY proses_order_detail_id 
                       ) f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');
       
        if(isset($filter['b']) &&  $filter['b'] !='' ){
             $this->datatables->where('id_bagian_asal', $filter['b']);
        }

         if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->datatables->where('g.melting_type_id', $filter['m']);
        }
       

        if(isset($filter['p']) &&  $filter['p'] !='0'  ){
            $this->datatables->where(' (DATE(a.waktu_in) <= \''.$filter['d'].'\' AND (DATE(a.waktu_out) > \''.$filter['d'].'\' OR `a`.`waktu_out` IS NULL ) )');
        }else{
             $this->datatables->where("a.status != 'finish' AND a.status != 'finish repair' AND a.status != 'close' ");
        }


         $this->datatables->group_by("a.id_proses_detail");
      //   $this->datatables->order_by("a.id_proses_detail");

        $this->datatables->add_column('action', anchor(site_url('laporan/read/$1'),'<small class="label bg-green"><i class="fa fa-search"></i></small>')."  ".anchor(site_url('laporan/update/$1'),'<small class="label bg-yellow"><i class="fa fa-pencil"></i></small>')."  ".anchor(site_url('laporan/delete/$1'),'<small class="label bg-red"><i class="fa fa-trash-o"></i></small>','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'id_proses_detail');
        return $this->datatables->generate();
    }



    function getFilter_item() {
        $tgl = date('Y-m-d');
        $filter = isset($_GET)? $_GET:'';
      //  $filter = $this->session->userdata('filter');
       if(isset($filter['d']) &&  $filter['d'] !='' ){
            $date = date('Y-m-d', strtotime($filter['d'] . ' +1 day')); 
            $where = 'WHERE input_date <  \''.$date.'\' ';
        }else{
            $where = '';
        }
        $this->db->select('CAST(LEFT(a.line_no,LOCATE(\'-\',a.line_no) - 1)  as UNSIGNED) as noka,CAST(RIGHT(a.line_no, LOCATE(\'-\',a.line_no)+1)  as UNSIGNED) as nokb,a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,a.item_number,a.barcode,a.metal,a.qty,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian, c.nama_bagian as pengirim, d.nama_bagian as tujuan,g.melting_type_id,h.title_melting_type,i.size,i.nilai,sum(dm.dia_carat) dia_carat,dm.note as dia_note,ms.stone_name,f.*');
        $this->db->from('tb_proses_order_detail a');
        //add this line for join
        //$this->datatables->join('table2', 'tb_proses_order_detail.field = table2.field');
        $this->db->join('master_bagian b', 'b.id_bagian=a.id_bagian_asal', 'left');
        $this->db->join('master_bagian c', 'c.id_bagian=a.id_bagian_pengirim', 'left');
        $this->db->join('master_bagian d', 'd.id_bagian=a.id_bagian_tujuan', 'left');
        $this->db->join('tb_diamond dm', 'dm.barcode  = a.barcode', 'left');
        $this->db->join('tb_proses_order g', 'g.id_proses  = a.proses_order_id', 'left');
        $this->db->join('tb_order_detail to', 'to.id_order_detail  = g.order_detail_id', 'left');
        $this->db->join('master_stone_type ms', 'ms.id  = to.stone_type', 'left');
        //$this->db->join('master_stone_type ms', 'ms.id  = to.stone_type', 'left');
      //  $this->db->join('tb_weight_proses f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');
        $this->db->join('master_melting_type h', 'h.id_melting_type  = g.melting_type_id', 'left');
        $this->db->join('master_size i', 'i.id  = h.size_id', 'left');
        $this->db->join('(SELECT
                            sum( gold_part ) AS out_gold_part,
                            sum( gold_chain ) AS out_gold_chain,
                            sum( other_part ) AS out_other_part,
                            sum( stone_weight ) AS out_stone_weight,
                            input_date,
                            proses_order_detail_id 
                        FROM
                            tb_weight_proses '.$where.'  GROUP BY proses_order_detail_id 
                       ) f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');
       
        if(isset($filter['b']) &&  $filter['b'] !='' ){
             $this->db->where('a.id_bagian_asal', $filter['b']);
        }

         if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->db->where('g.melting_type_id', $filter['m']);
        }
       

        if(isset($filter['p']) &&  $filter['p'] !='0'  ){
            $this->db->where('a.status != \'close\' AND DATE(a.waktu_in) <= \''.$filter['d'].'\' AND ( (a.status = \'sending\' OR a.status = \'reject\') OR ((DATE(a.waktu_out) > \''.$filter['d'].'\' OR `a`.`waktu_out` IS NULL ) ) )');
        }else{
             $this->db->where("a.status != 'finish' AND a.status != 'finish repair' AND a.status != 'close' AND a.status != 'casting' AND a.status != 'cutting' AND a.status != 'send cutting' AND a.status != 'send casting'");
        }


        $this->db->group_by("a.id_proses_detail");
        $this->db->order_by("noka");
        $this->db->order_by("nokb");
        $hasil = $this->db->get()->result();
        $query = $this->db->last_query();    
         return  $hasil;
    }



    function getFilter_loss_bagian() {
        $tgl = date('Y-m-d');
        $filter = $this->session->userdata('filter');
        if(isset($filter['d']) &&  $filter['d'] !='' ){
            $date = date('Y-m-d', strtotime($filter['d'] . ' +1 day')); 
            $where = 'WHERE input_date <  \''.$date.'\'';
        }else{
            $where = '';
        }
        $this->db->select('a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,e.item_number,a.barcode,a.metal,a.qty,a.id_bagian_pengirim,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian,c.nama_bagian as pengirim,d.nama_bagian as tujuan,g.melting_type_id,h.title_melting_type,i.size,i.nilai,f.*');
        $this->db->from('tb_proses_order_detail a');
         $this->db->join('master_item e', 'e.id_item  = a.item_id', 'left');
        //add this line for join
        //$this->datatables->join('table2', 'tb_proses_order_detail.field = table2.field');
        $this->db->join('master_bagian b', 'b.id_bagian=a.id_bagian_asal', 'left');
        $this->db->join('master_bagian c', 'c.id_bagian=a.id_bagian_pengirim', 'left');
        $this->db->join('master_bagian d', 'd.id_bagian=a.id_bagian_tujuan', 'left');
        
      //  $this->db->join('tb_weight_proses f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');
        $this->db->join('tb_proses_order g', 'g.id_proses  = a.proses_order_id', 'left');
        $this->db->join('master_melting_type h', 'h.id_melting_type  = g.melting_type_id', 'left');
        $this->db->join('master_size i', 'i.id  = h.size_id', 'left');
        $this->db->join('(SELECT
                    sum( gold_part ) AS out_gold_part,
                    sum( gold_chain ) AS out_gold_chain,
                    sum( other_part ) AS out_other_part,
                    sum( stone_weight ) AS out_stone_weight,
                    input_date,
                    proses_order_detail_id 
                FROM
                    tb_weight_proses '.$where.'  GROUP BY proses_order_detail_id 
               ) f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');

        if(isset($filter['b']) &&  $filter['b'] !='' ){
             $this->datatables->where("id_bagian_asal = '".$filter['b']."' AND (a.status = 'finish' OR a.status = 'finish repair'  OR a.status = 'sending repair'  OR a.status = 'sending') ");
        }else{
            $user = $this->ion_auth->get_user();
            $bagian =  $user->id_bagian;
            $this->datatables->where("id_bagian_asal = '".$bagian."' AND (a.status = 'finish' OR a.status = 'finish repair'  OR a.status = 'sending repair'  OR a.status = 'sending') ");
        }
        
 
       if(isset($filter['d']) &&  $filter['d'] !='' ){
             $this->db->where('DATE(a.waktu_out) = \''.$filter['d'].'\'');
        }
         if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->db->where('g.melting_type_id', $filter['m']);
        }
        $this->db->group_by("a.id_proses_detail");        
        $hasil = $this->db->get()->result();
        $query = $this->db->last_query();   
      //  echo ($query); 
         return  $hasil;
    }




    function getFilter_sisa_produksi() {
        $tgl = date('Y-m-d');
        $filter = $this->session->userdata('filter');
        if(isset($filter['d']) &&  $filter['d'] !='' ){
            $date = $filter['d']; 
        }else{
            $date = '';
        }
        if(isset($filter['b']) &&  $filter['b'] !='' ){
            $bag = $filter['b']; 
        }else{
            $bag = '';
        }
        $sql = "SELECT
                mt.id_melting_type,mt.title_melting_type,mz.nilai,od.powder,od.nancip,od.potongan
            FROM
                master_melting_type mt
                LEFT JOIN master_size mz ON mz.id = mt.size_id
                LEFT JOIN (
                SELECT DISTINCT
                    ( `a`.`id_proses_detail` ),
                    `a`.`id_bagian_asal`,
                    `a`.`status`,
                    `a`.`waktu_out`,
                    sum( `a`.`powder` ) powder,
                    sum( `a`.`nancip` ) nancip,
                    sum( `a`.`potongan` ) potongan,
                    `g`.`melting_type_id` 
                FROM
                    `tb_proses_order_detail` `a`
                    LEFT JOIN `tb_proses_order` `g` ON `g`.`id_proses` = `a`.`proses_order_id` 
                WHERE
                    `id_bagian_asal` = '$bag' 
                    AND ( `a`.`status` = 'finish' OR `a`.`status` = 'finish repair' OR `a`.`status` = 'sending repair' OR `a`.`status` = 'sending' ) 
                    AND DATE( a.waktu_out ) = '$date' 
                GROUP BY
                `g`.melting_type_id 
                ) od ON od.melting_type_id = mt.id_melting_type";     
        $query = $this->db->query($sql);
        $hasil = $query->result();
        $query = $this->db->last_query();   
      //  echo ($query); 
         return  $hasil;
    }


    function getFilter_komponen() {
      $tgl = date('Y-m-d');
        $this->db->select('di.dia_carat,d.melting_type_id,ms.stone_name as remark,b.id_proses_detail,b.no_po,b.line_no,b.waktu_proses,c.item_number,b.barcode,b.metal,b.qty,b.id_bagian_asal,b.id_bagian_tujuan,b.wax_id,b.casting_id,b.status,b.waktu_in,b.waktu_out,b.in_weight,b.gold_weight_in,b.out_weight,b.powder,b.nancip,b.potongan,b.gold_weight_out,b.loss,b.gold_replace,b.note_asal,b.note_tujuan,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,');
        $this->db->from('tb_weight_proses a');
        $this->db->join('tb_proses_order_detail b', 'b.id_proses_detail=a.proses_order_detail_id', 'left');
        $this->db->join('master_item c', 'c.id_item  = b.item_id', 'left');
        $this->db->join('tb_proses_order d', 'd.id_proses  = b.proses_order_id', 'left');
        $this->db->join('(select barcode,sum(dia_carat) dia_carat from tb_diamond group by barcode) di', 'b.barcode  = di.barcode', 'left');
         $this->db->join('tb_order_detail to', 'to.id_order_detail  = d.order_detail_id', 'left');
        $this->db->join('master_stone_type ms', 'ms.id  = to.stone_type', 'left');

         $filter = $this->session->userdata('filter');
        if(isset($filter['b']) &&  $filter['b'] !='' ){
            $this->db->where("b.id_bagian_asal = '".$filter['b']."' ");
        }else{
    
        }
        
       if(isset($filter['d']) &&  $filter['d'] !='' ){
             $this->db->where('DATE(a.input_date) = \''.$filter['d'].'\'');
        }

        if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->db->where('d.melting_type_id', $filter['m']);
        }   
        $hasil = $this->db->get()->result();
        $query = $this->db->last_query();   
      //  echo ($query); 
         return  $hasil;
    }

    function in_part($date,$bagian) {
            $tgl = date('Y-m-d');
            $filter = $this->session->userdata('filter');
            if($bagian == 'all' OR $bagian ==''){
                $wherebagian = ' WHERE ';
            }else{
                 $wherebagian = 'WHERE b.id_bagian_asal ='.$bagian.' AND ';
            }
            if(isset($filter['d']) &&  $filter['d'] !='' ){
                $wdate = date('Y-m-d', strtotime($filter['d'] . ' +1 day')); 
                $where = 'WHERE input_date <  \''.$wdate.'\' ';
            }else{
                $where = '';
            }

        $sql = "SELECT
                    d.melting_type_id,
                    `b`.`id_bagian_asal`,
                    sum(`a`.`gold_part`) as gold_part,
                    sum(`a`.`gold_chain`) as gold_chain,
                    sum(`a`.`other_part`) as other_part,
                    sum(`a`.`stone_weight`) as stone_weight 
                FROM
                    `tb_weight_proses` `a`
                    LEFT JOIN `tb_proses_order_detail` `b` ON `b`.`id_proses_detail` = `a`.`proses_order_detail_id`
                    LEFT JOIN `tb_proses_order` `d` ON `d`.`id_proses` = `b`.`proses_order_id` 
                    $wherebagian 
                    DATE( a.input_date ) = '$date' 
                GROUP BY  d.melting_type_id";

        $query = $this->db->query($sql);
        $hasil = $query->result();
        $query = $this->db->last_query();   
      //  echo ($query); 
         return  $hasil;
    }

    function iventory_in_out($date,$bagian) {
        $tgl = date('Y-m-d');
        $filter = $this->session->userdata('filter');
        $prevdate = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        $prevdate2 = date('Y-m-d', strtotime('-1 day', strtotime($prevdate)));
        $pdate = date('Y-m-d', strtotime('+1 day', strtotime($date)));
        $nowdate = date('Y-m-d', strtotime('+1 day',now() ));
        if($bagian == 'all' OR $bagian ==''){
            $wherebagian = '';
        }else{
             $wherebagian = 'AND pod.id_bagian_asal ='.$bagian.' ';
        }
        if(isset($filter['d']) &&  $filter['d'] !='' ){
            $wdate = date('Y-m-d', strtotime($filter['d'] . ' +1 day')); 
            $where = 'WHERE input_date <  \''.$wdate.'\' ';
        }else{
            $where = '';
        }

        $bagianid = isset($bagian)? $bagian:'ALL';
        $sql = "SELECT
    melting_type_id,
    sum( qty_in ) qty_in,
    sum( gold_in ) gold_in,
    sum(new_gold_in) new_gold_in,
    sum(in_weight) in_weight,
    sum( qty_out ) qty_out,
    sum( gold_out ) gold_out,
    sum( out_gold_part ) out_gold_part,
    sum( out_gold_chain ) out_gold_chain,
    sum( powder ) AS powder,
    sum( nancip ) AS nancip,
    sum( potongan ) AS potongan 
FROM
    (
    SELECT
        pod.id_proses_detail AS id_in,
        po.melting_type_id,
        pod.metal AS metal_in,
        pod.id_bagian_asal AS id_bagian_asal,
        pod.gold_weight_in,
        pod.qty AS qty,
        @b :=
    IF
        ( (DATE( pod.waktu_in ) = '$date' ), pod.qty, 0 ) AS qty_in,
        @c :=
    IF
        ( (DATE( pod.waktu_out ) = '$date'  AND pod.status != 'reject'), pod.qty, 0 ) AS qty_out,
        @f :=
    IF
        ( (DATE( pod.waktu_out ) = '$date' AND pod.status != 'reject'), powder, 0 ) AS powder,
        @g :=
    IF
        ( (DATE( pod.waktu_out ) = '$date'  AND pod.status != 'reject'), nancip, 0 ) AS nancip,
        @h :=
    IF
        ( (DATE( pod.waktu_out ) = '$date'  AND pod.status != 'reject'), potongan, 0 ) AS potongan,
        pod.loss,
        DATE( pod.waktu_in ) AS `waktu_in`,
        DATE( pod.waktu_out ) AS `waktu_out`,
        @in_weight := COALESCE ( pod.in_weight, 0 ) AS in_weight,

    @out_gold_part := COALESCE ( out_gold_part, 0 ) AS out_gold_part,
    @out_gold_chain := COALESCE ( out_gold_chain, 0 ) AS out_gold_chain,
    @out_other_part := COALESCE ( out_other_part, 0 ) AS out_other_part,
    @out_stone_weight := COALESCE ( out_stone_weight, 0 ) AS out_stone_weight,
    @other_part := COALESCE ( other_part, 0 ) AS other_part,
    @stone_weight := COALESCE ( stone_weight, 0 ) AS stone_weight,
    ( in_weight + @out_gold_part + @out_gold_chain + @out_other_part + @out_stone_weight ) AS in_weight_now,
    ( @other_part + @out_other_part ) AS other_part_now,
    ( @stone_weight + @out_stone_weight ) AS stone_now,
    @d :=
    IF
        (
            DATE( pod.waktu_in ) = '$date',
            ( in_weight + @out_gold_part + @out_gold_chain + @out_other_part + @out_stone_weight ) - ( @other_part + @out_other_part ) - ( @stone_weight + @out_stone_weight ) ,
            0 
        ) AS gold_in,

        @e :=
    IF
        ( (DATE( pod.waktu_out ) = '$date' AND pod.status != 'reject') , gold_weight_out, 0 ) AS gold_out, 
                    @j :=
        IF
            ( ( DATE( pod.waktu_in ) = '$date'), IF( id_bagian_pengirim = 6 AND gold_weight_in = 0 ,in_weight ,gold_weight_in )    , 0 ) AS new_gold_in
    FROM
        tb_proses_order_detail pod
        LEFT JOIN tb_proses_order po ON po.id_proses = pod.proses_order_id
        LEFT JOIN (
        SELECT
            sum( gold_part ) AS out_gold_part,
            sum( gold_chain ) AS out_gold_chain,
            sum( other_part ) AS out_other_part,
            sum( stone_weight ) AS out_stone_weight,
            input_date,
            proses_order_detail_id 
        FROM
            tb_weight_proses 
        WHERE
            input_date < '$pdate' 
        GROUP BY
            proses_order_detail_id 
        ) f ON pod.`id_proses_detail` = `f`.`proses_order_detail_id` 
    WHERE
        ( DATE( pod.waktu_in ) = '$date' OR DATE( pod.waktu_out ) = '$date' ) 
        $wherebagian
    ) in_out 
GROUP BY
    melting_type_id";     
        $query = $this->db->query($sql);
        $hasil = $query->result();
        $query = $this->db->last_query();   
      //  echo ($query); 
         return  $hasil;
    }

    function iventory_sisa($date,$bagian) {

        $tgl = date('Y-m-d');

        $filter = $this->session->userdata('filter');
        $oldate = $date;
        $prevdate = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        $prevdate2 = date('Y-m-d', strtotime('-1 day', strtotime($prevdate)));
        $pdate = date('Y-m-d', strtotime('+1 day', strtotime($date)));
        $nowdate = date('Y-m-d', strtotime('+1 day',now() ));
        if($bagian == 'all' OR $bagian ==''){
            $wherebagian = '';
        }else{
             $wherebagian = 'id_bagian_asal = '.$bagian.' AND ';
        }
        
        if(isset($date) &&  $date !='' ){
            $datew = date('Y-m-d', strtotime($date . ' +1 day')); 
            $where = 'WHERE input_date <  \''.$datew.'\' ';
        }else{
            $where = '';
        }

        $bagianid = isset($bagian)? $bagian:13;
        $sql = "SELECT
    melting_type_id,metal,
    ROUND( sum( only_gold ), 2 ) AS gold_sisa,
    sum( qty ) AS qty_sisa 
FROM
    (
    SELECT
    `a`.`id_proses_detail`,
    `a`.`proses_order_id`,
    `a`.`no_po`,
    `a`.`line_no`,
    `a`.`waktu_proses`,
    `e`.`item_number`,
    `a`.`barcode`,
    `a`.`metal`,
    `a`.`qty`,
    `a`.`id_bagian_asal`,
    `a`.`id_bagian_tujuan`,
    `a`.`wax_id`,
    `a`.`casting_id`,
    `a`.`status`,
    `a`.`waktu_in`,
    `a`.`waktu_out`,
    `a`.`note_asal`,
    `a`.`note_tujuan`,
    `b`.`nama_bagian`,
    `c`.`nama_bagian` AS `pengirim`,
    `d`.`nama_bagian` AS `tujuan`,
    `g`.`melting_type_id`,
    `h`.`title_melting_type`,
    `i`.`size`,
    `i`.`nilai`,
    f.input_date,
    f.proses_order_detail_id,
    `a`.`in_weight`,
    `a`.`gold_part`,
    `a`.`gold_chain`,
    `a`.`gold_weight_in`,
    `a`.`out_weight`,
    `a`.`powder`,
    `a`.`nancip`,
    `a`.`potongan`,
    `a`.`gold_weight_out`,
    `a`.`loss`,
    `a`.`gold_replace`,
    @out_gold_part := COALESCE ( out_gold_part, 0 ) AS out_gold_part,
    @out_gold_chain := COALESCE ( out_gold_chain, 0 ) AS out_gold_chain,
    @out_other_part := COALESCE ( out_other_part, 0 ) AS out_other_part,
    @out_stone_weight := COALESCE ( out_stone_weight, 0 ) AS out_stone_weight,
    @other_part := COALESCE ( other_part, 0 ) AS other_part,
    @stone_weight := COALESCE ( stone_weight, 0 ) AS stone_weight,
    ( in_weight + @out_gold_part + @out_gold_chain + @out_other_part + @out_stone_weight ) AS in_weight_now,
    ( @other_part + @out_other_part ) AS other_part_now,
    ( @stone_weight + @out_stone_weight ) AS stone_now,
    (
        ( in_weight + @out_gold_part + @out_gold_chain + @out_other_part + @out_stone_weight ) - ( @other_part + @out_other_part ) - ( @stone_weight + @out_stone_weight ) 
    ) AS only_gold 
FROM
    `tb_proses_order_detail` `a`
    LEFT JOIN `master_bagian` `b` ON `b`.`id_bagian` = `a`.`id_bagian_asal`
    LEFT JOIN `master_bagian` `c` ON `c`.`id_bagian` = `a`.`id_bagian_pengirim`
    LEFT JOIN `master_bagian` `d` ON `d`.`id_bagian` = `a`.`id_bagian_tujuan`
    LEFT JOIN `master_item` `e` ON `e`.`id_item` = `a`.`item_id`
    LEFT JOIN `tb_proses_order` `g` ON `g`.`id_proses` = `a`.`proses_order_id`
    LEFT JOIN `master_melting_type` `h` ON `h`.`id_melting_type` = `g`.`melting_type_id`
    LEFT JOIN `master_size` `i` ON `i`.`id` = `h`.`size_id`
    LEFT JOIN (
    SELECT
        sum( gold_part ) AS out_gold_part,
        sum( gold_chain ) AS out_gold_chain,
        sum( other_part ) AS out_other_part,
        sum( stone_weight ) AS out_stone_weight,
        input_date,
        proses_order_detail_id 
    FROM
        tb_weight_proses 
    $where
    GROUP BY
        proses_order_detail_id 
    ) f ON `a`.`id_proses_detail` = `f`.`proses_order_detail_id` 
WHERE
    $wherebagian  a.status != 'close' AND DATE( a.waktu_in ) <= '$oldate' AND ( (a.status = 'sending' OR a.status = 'reject') OR  ( DATE( a.waktu_out ) > '$oldate' OR `a`.`waktu_out` IS NULL ) )
GROUP BY
    `a`.`id_proses_detail` 
ORDER BY
    `a`.id_proses_detail
    ) cinta 
GROUP BY
    melting_type_id"; 

//                    AND waktu_in < '$pdate' AND ( ( waktu_out BETWEEN '$date' AND '$nowdate' ) OR waktu_out IS NULL OR waktu_out = '' ) 

        $query = $this->db->query($sql);
        $hasil = $query->result();
        $query = $this->db->last_query();   
       // echo ($query); 
       // echo "</br>#####################################</br>";
        unset($date,$oldate,$pdate,$datew,$where);
         return  $hasil;
    }






    function getFilter_iventory() {
        $filter = $this->session->userdata('filter');
        if(isset($filter['d']) &&  $filter['d'] !='' ){
            $date = date('Y-m-d', strtotime($filter['d'])); 

        }else{
            $date = '';
        }
        $prevdate = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        $prevdate2 = date('Y-m-d', strtotime('-1 day', strtotime($prevdate)));
        $pdate = date('Y-m-d', strtotime('+1 day', strtotime($date)));
        $nowdate = date('Y-m-d', strtotime('+1 day',now() ));
        $bagianid = isset($filter['b'])? $filter['b']:13;
        if($bagianid == 'all'){
            $wherebagian = '';
            $wherebagian2 = '';
        }else{
             $wherebagian = ' AND pod.id_bagian_asal ='.$bagianid;
             $wherebagian2 = ' id_bagian_asal ='.$bagianid.' AND ';
        }
        $sql = "SELECT
    mm.*,
    mz.size,
    mz.nilai,
    COALESCE ( in_out.qty_in, 0 ) AS qty_in,
    COALESCE ( in_out.qty_out, 0 ) AS qty_out,
    COALESCE ( in_out.gold_in, 0 ) AS gold_in,
    COALESCE ( in_out.gold_out, 0 ) AS gold_out,
    COALESCE ( in_out.nancip, 0 ) AS nancip,
    COALESCE ( in_out.powder, 0 ) AS powder,
    COALESCE ( in_out.potongan, 0 ) AS potongan,
    COALESCE ( sisa.qty_real, 0 ) AS qty_real,
    COALESCE ( sisa.gold_real, 0 ) AS gold_real,
    COALESCE ( stock.qty_stock, 0 ) AS qty_stock,
    COALESCE ( stock.gold_stock, 0 ) AS gold_stock
FROM
    master_melting_type mm
    LEFT JOIN master_size mz ON mz.id = mm.size_id
    LEFT JOIN (
    SELECT
        melting_type_id,
        metal_in,
        bagian,
        sum( qty_in ) AS qty_in,
        sum( qty_out ) AS qty_out,
        sum( gold_in ) gold_in,
        sum( gold_out ) gold_out,
        sum( powder ) powder,
        sum( nancip ) nancip,
        sum( potongan ) potongan,
        sum( loss ) loss,
        waktu_in,
        waktu_out 
    FROM
        (
        SELECT
            pod.id_proses_detail AS id_in,
            po.melting_type_id,
            pod.metal AS metal_in,
            pod.id_bagian_asal AS bagian,
            pod.qty AS qty,
            @b :=
        IF
            ( cast( pod.waktu_in AS date ) = '$date', pod.qty, 0 ) AS qty_in,
            @c :=
        IF
            ( cast( pod.waktu_out AS date ) = '$date', pod.qty, 0 ) AS qty_out,
            @e :=
        IF
            ( cast( pod.waktu_out AS date ) = '$date', gold_weight_out, 0 ) AS gold_out,
            @f :=
        IF
            ( cast( pod.waktu_out AS date ) = '$date', powder, 0 ) AS powder,
            @g :=
        IF
            ( cast( pod.waktu_out AS date ) = '$date', nancip, 0 ) AS nancip,
            @h :=
        IF
            ( cast( pod.waktu_out AS date ) = '$date', potongan, 0 ) AS potongan,
            pod.loss,
            cast( pod.waktu_in AS date ) AS `waktu_in`,
            cast( pod.waktu_out AS date ) AS `waktu_out`,
            @out_gold_part := COALESCE ( `f`.out_gold_part, 0 ) AS out_gold_part,
            @out_gold_chain := COALESCE ( `f`.out_gold_chain, 0 ) AS out_gold_chain,
            @out_other_part := COALESCE ( `f`.out_other_part, 0 ) AS out_other_part,
            @out_stone_weight := COALESCE ( `f`.out_stone_weight, 0 ) AS out_stone_weight,
            @d :=
        IF
            (
                cast( pod.waktu_in AS date ) = '$date',
                ( in_weight + @out_gold_part + @out_gold_chain + @out_other_part + @out_stone_weight ),
                0 
            ) AS gold_in
        FROM
            tb_proses_order_detail pod
            LEFT JOIN tb_proses_order po ON po.id_proses = pod.proses_order_id
            LEFT JOIN (
            SELECT
                sum( gold_part ) AS out_gold_part,
                sum( gold_chain ) AS out_gold_chain,
                sum( other_part ) AS out_other_part,
                sum( stone_weight ) AS out_stone_weight,
                input_date,
                proses_order_detail_id 
            FROM
                tb_weight_proses 
            WHERE
                input_date < '$pdate' 
            GROUP BY
                proses_order_detail_id 
            ) f ON pod.`id_proses_detail` = `f`.`proses_order_detail_id` 
        WHERE
            ( cast( pod.waktu_in AS date ) = '$date' OR cast( pod.waktu_out AS date ) = '$date' ) 
            $wherebagian 
        ) AS invetory 
    GROUP BY
        melting_type_id 
    ) in_out ON in_out.melting_type_id = mm.id_melting_type
    LEFT JOIN (
    SELECT
        sum( qty ) AS qty_real,
        melting_type_id,
        id_bagian_asal,
        ROUND( sum( only_gold ), 2 ) AS gold_real 
    FROM
        (
        SELECT
            `a`.`id_proses_detail`,
            `a`.`id_bagian_asal`,
            `g`.`melting_type_id`,
            `a`.`qty`,
            `a`.`waktu_in`,
            `a`.`waktu_out`,
            @in_weight := COALESCE ( `a`.`in_weight`, 0 ) AS in_weight,
            @gold_part := COALESCE ( `a`.`gold_part`, 0 ) AS gold_part,
            @gold_chain := COALESCE ( `a`.`gold_chain`, 0 ) AS gold_chain,
            @other_part := COALESCE ( `a`.`other_part`, 0 ) AS other_part,
            @stone_weight := COALESCE ( `a`.`stone_weight`, 0 ) AS stone_weight,
            @gold_weight_in := COALESCE ( `a`.`gold_weight_in`, 0 ) AS gold_weight_in,
            @gold_weight_out := COALESCE ( `a`.`gold_weight_out`, 0 ) AS gold_weight_out,
            @out_weight := COALESCE ( `a`.`out_weight`, 0 ) AS out_weight,
            @powder := COALESCE ( `a`.`powder`, 0 ),
            @nancip := COALESCE ( `a`.`nancip`, 0 ),
            @potongan := COALESCE ( `a`.`potongan`, 0 ),
            @out_gold_part := COALESCE ( `f`.out_gold_part, 0 ) AS out_gold_part,
            @out_gold_chain := COALESCE ( `f`.out_gold_chain, 0 ) AS out_gold_chain,
            @out_other_part := COALESCE ( `f`.out_other_part, 0 ) AS out_other_part,
            @out_stone_weight := COALESCE ( `f`.out_stone_weight, 0 ) AS out_stone_weight,
            @loss := COALESCE ( `a`.`loss`, 0 ),
            @gold_replace := COALESCE ( `a`.`gold_replace`, 0 ) AS gold_replace,
            ( @in_weight + @out_gold_part + @out_gold_chain + @out_other_part + @out_stone_weight ) AS in_weight_now,
            ( @other_part + @out_other_part ) AS other_part_now,
            ( @stone_weight + @out_stone_weight ) AS stone_now,
            (
                ( @gold_weight_in + @out_gold_part + @out_gold_chain + @out_other_part + @out_stone_weight ) - ( @other_part + @out_other_part ) - ( @stone_weight + @out_stone_weight ) 
            ) AS only_gold 
        FROM
            `tb_proses_order_detail` `a`
            LEFT JOIN `tb_proses_order` `g` ON `g`.`id_proses` = `a`.`proses_order_id`
            LEFT JOIN (
            SELECT
                sum( gold_part ) AS out_gold_part,
                sum( gold_chain ) AS out_gold_chain,
                sum( other_part ) AS out_other_part,
                sum( stone_weight ) AS out_stone_weight,
                input_date,
                proses_order_detail_id 
            FROM
                tb_weight_proses 
            WHERE
                DATE(input_date) < '$pdate' 
            GROUP BY
                proses_order_detail_id 
            ) f ON `a`.`id_proses_detail` = `f`.`proses_order_detail_id` 
        WHERE
            $wherebagian2
            DATE(waktu_in) < '$pdate' 
            AND ( ( waktu_out BETWEEN '$date' AND '$nowdate') OR waktu_out IS NULL OR waktu_out = '' ) 
        GROUP BY
            `a`.`id_proses_detail` 
        ) stockb 
    GROUP BY
        melting_type_id 
    ) sisa ON sisa.melting_type_id = mm.id_melting_type
    LEFT JOIN (
    SELECT
        sum( qty ) AS qty_stock,
        melting_type_id,
        id_bagian_asal,
        ROUND( sum( only_gold ), 2 ) AS gold_stock 
    FROM
        (
        SELECT
            `a`.`id_proses_detail`,
            `a`.`id_bagian_asal`,
            `g`.`melting_type_id`,
            `a`.`qty`,
            `a`.`waktu_in`,
            `a`.`waktu_out`,
            @in_weight := COALESCE ( `a`.`in_weight`, 0 ) AS in_weight,
            @gold_part := COALESCE ( `a`.`gold_part`, 0 ) AS gold_part,
            @gold_chain := COALESCE ( `a`.`gold_chain`, 0 ) AS gold_chain,
            @other_part := COALESCE ( `a`.`other_part`, 0 ) AS other_part,
            @stone_weight := COALESCE ( `a`.`stone_weight`, 0 ) AS stone_weight,
            @gold_weight_in := COALESCE ( `a`.`gold_weight_in`, 0 ) AS gold_weight_in,
            @gold_weight_out := COALESCE ( `a`.`gold_weight_out`, 0 ) AS gold_weight_out,
            @out_weight := COALESCE ( `a`.`out_weight`, 0 ) AS out_weight,
            @powder := COALESCE ( `a`.`powder`, 0 ) AS powder,
            @nancip := COALESCE ( `a`.`nancip`, 0 ) AS nancip,
            @potongan := COALESCE ( `a`.`potongan`, 0 ) AS potongan,
            @out_gold_part := COALESCE ( `f`.out_gold_part, 0 ) AS out_gold_part,
            @out_gold_chain := COALESCE ( `f`.out_gold_chain, 0 ) AS out_gold_chain,
            @out_other_part := COALESCE ( `f`.out_other_part, 0 ) AS out_other_part,
            @out_stone_weight := COALESCE ( `f`.out_stone_weight, 0 ) AS out_stone_weight,
            @loss := COALESCE ( `a`.`loss`, 0 ),
            @gold_replace := COALESCE ( `a`.`gold_replace`, 0 ) AS gold_replace,
            (
                ( @in_weight + @out_gold_part + @out_gold_chain + @out_other_part +@out_stone_weight ) - ( @other_part + @out_other_part ) - ( @stone_weight + @out_stone_weight ) 
            ) AS only_gold 
        FROM
            `tb_proses_order_detail` `a`
            LEFT JOIN `tb_proses_order` `g` ON `g`.`id_proses` = `a`.`proses_order_id`
            LEFT JOIN (
            SELECT
                sum( gold_part ) AS out_gold_part,
                sum( gold_chain ) AS out_gold_chain,
                sum( other_part ) AS out_other_part,
                sum( stone_weight ) AS out_stone_weight,
                input_date,
                proses_order_detail_id 
            FROM
                tb_weight_proses 
            WHERE
                input_date < '$date' 
            GROUP BY
                proses_order_detail_id 
            ) f ON `a`.`id_proses_detail` = `f`.`proses_order_detail_id` 
        WHERE
            $wherebagian2 
            DATE(waktu_in) < '$prevdate' 
            AND ( ( waktu_out BETWEEN '$prevdate' AND '$nowdate') OR waktu_out IS NULL OR waktu_out = '' ) 
        GROUP BY
            `a`.`id_proses_detail` 
        ) stock 
    GROUP BY
    melting_type_id 
    ) stock ON stock.melting_type_id = mm.id_melting_type";
        $query = $this->db->query($sql);
        $hasil = $query->result();
        $lquery = $this->db->last_query();    
      // echo var_dump($sql);
         return  $hasil;
    }
    
    
   function json_komponen($bagian=5) {
        $tgl = date('Y-m-d');
        $this->datatables->select('d.melting_type_id,b.id_proses_detail,b.no_po,b.line_no,b.waktu_proses,c.item_number,b.barcode,b.metal,b.qty,b.id_bagian_asal,b.id_bagian_tujuan,b.wax_id,b.casting_id,b.status,b.waktu_in,b.waktu_out,b.in_weight,b.gold_weight_in,b.out_weight,b.powder,b.nancip,b.potongan,b.gold_weight_out,b.loss,b.gold_replace,b.note_asal,b.note_tujuan,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.input_date');
        $this->datatables->from('tb_weight_proses a');
        $this->datatables->join('tb_proses_order_detail b', 'b.id_proses_detail=a.proses_order_detail_id', 'left');
        $this->datatables->join('master_item c', 'c.id_item  = b.item_id', 'left');
        $this->datatables->join('tb_proses_order d', 'd.id_proses  = b.proses_order_id', 'left');

         $filter = $this->session->userdata('filter');
        if(isset($filter['b']) &&  $filter['b'] !='' ){
            $this->datatables->where("b.id_bagian_asal = '".$filter['b']."' ");
        }else{
    
        }
        
       if(isset($filter['d']) &&  $filter['d'] !='' ){
             $this->datatables->where('DATE(a.input_date) = \''.$filter['d'].'\'');
        }

        if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->datatables->where('d.melting_type_id', $filter['m']);
        }
        
        $this->datatables->add_column('action', anchor(site_url('laporan/read/$1'),'<small class="label bg-green"><i class="fa fa-search"></i></small>')."  ".anchor(site_url('laporan/update/$1'),'<small class="label bg-yellow"><i class="fa fa-pencil"></i></small>')."  ".anchor(site_url('laporan/delete/$1'),'<small class="label bg-red"><i class="fa fa-trash-o"></i></small>','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'id_proses_detail');
        return $this->datatables->generate();
    }


   function json_bystatus() {
        $tgl = date('Y-m-d');
        $this->datatables->select('a.*');
        $this->datatables->from('view_proses a');
        $filter = $this->session->userdata('filter');
       
       if(isset($filter['d']) &&  $filter['d'] !='' ){
          //   $this->datatables->where('DATE(a.waktu_out) = \''.$filter['d'].'\'');
        }

        if(isset($filter['s']) &&  $filter['s'] !='' ){
             $this->datatables->where('a.status', strtolower($filter['s']));
        }
        
        return $this->datatables->generate();
    }

  function getFilter_status() {
        $tgl = date('Y-m-d');
        $this->db->select('a.*');
        $this->db->from('view_proses a');
        $filter = $this->session->userdata('filter');
       
       if(isset($filter['d']) &&  $filter['d'] !='' ){
          //   $this->datatables->where('DATE(a.waktu_out) = \''.$filter['d'].'\'');
        }

        if(isset($filter['s']) &&  $filter['s'] !='' ){
             $this->db->where('a.status', strtolower($filter['s']));
        }
        $this->db->order_by('a.no_po,line_no','ASC');
        return $this->db->get()->result();
    }




    function getFilter() {
        $filter = $this->session->userdata('filter');
     if(isset($filter['d']) &&  $filter['d'] !='' ){
            $date = date('Y-m-d', strtotime($filter['d'] . ' +1 day')); 
            $where = 'WHERE input_date <  \''.$date.'\'';
        }else{
            $where = '';
        }


        $this->db->select('a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,c.item_number,a.barcode,a.metal,a.qty,a.id_bagian_pengirim,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian,b.nama_bagian as pengirim,ba.nama_bagian as tujuan,f.*,g.melting_type_id,h.title_melting_type,i.size,i.nilai');
        $this->db->from('tb_proses_order_detail a');
        $this->db->join('master_bagian b', 'b.id_bagian=a.id_bagian_pengirim', 'left');
        $this->db->join('master_bagian ba', 'ba.id_bagian=a.id_bagian_tujuan', 'left');
        $this->db->join('master_item c', 'c.id_item  = a.item_id', 'left');

       // $this->db->join('tb_weight_proses f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');
        $this->db->join('tb_proses_order g', 'g.id_proses  = a.proses_order_id', 'left');
        $this->db->join('master_melting_type h', 'h.id_melting_type  = g.melting_type_id', 'left');
        $this->db->join('master_size i', 'i.id  = h.size_id', 'left');
        if(isset($filter['po']) &&  ($filter['po'] !=''  ) ){
             $this->db->where('a.no_po', $filter['po']);
             $where = '';
             if($filter['s'] != 'in'){
                $this->db->where('(a.status = \'finish\' OR a.status = \'finish repair\')');
            }
        }else{
        
            if(isset($filter['s']) &&  $filter['s'] !='' ){
                if($filter['s'] == 'in'){
                    if(isset($filter['d']) &&  $filter['d'] !='' ){
                         $this->db->where('DATE(waktu_in) = \''.$filter['d'].'\'');
                    }
                }else{
                     $this->db->where('a.status != \'reject\' ');
                     if(isset($filter['d']) &&  $filter['d'] !='' ){
                         $this->db->where('(a.status = \'finish\' OR a.status = \'finish repair\')');
                         $this->db->where('DATE(waktu_out) = \''.$filter['d'].'\'');
                    }
                }
               //
            }

        }

        $this->db->join('(SELECT
                            sum( gold_part ) AS out_gold_part,
                            sum( gold_chain ) AS out_gold_chain,
                            sum( other_part ) AS out_other_part,
                            sum( stone_weight ) AS out_stone_weight,
                            input_date,
                            proses_order_detail_id 
                        FROM
                            tb_weight_proses '.$where.'  GROUP BY proses_order_detail_id 
                       ) f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');



        if(isset($filter['b']) &&  $filter['b'] !='' ){
             $this->db->where('id_bagian_asal', $filter['b']);
        }

         if(isset($filter['m']) &&  ($filter['m'] !='' && $filter['m'] !='all'  ) ){
             $this->db->where('g.melting_type_id', $filter['m']);
        }

   //    $this->db->where("a.status = 'finish' OR a.status = 'finish repair' ");
        $this->db->group_by("a.id_proses_detail");
        $hasil = $this->db->get()->result();
        $query = $this->db->last_query();    
      // echo var_dump($query);
//die();
         return  $hasil;
    }


 function getLoss() {
        $this->db->select('a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,e.item_number,a.barcode,a.metal,a.qty,a.id_bagian_pengirim,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian');
        $this->db->from('tb_proses_order_detail a');
        $this->db->join('master_bagian b', 'b.id_bagian=a.id_bagian_pengirim', 'left');
        $this->db->join('tb_proses_order c', 'c.id_proses=a.proses_order_id', 'left');
        $this->db->join('master_melting_type d', 'd.id_melting_type=c.melting_type_id', 'left');
         $this->db->join('master_item e', 'e.id_item  = a.item_id', 'left');

        $filter = $this->session->userdata('filter');

        if(isset($filter['s']) &&  $filter['s'] !='' ){
            $this->db->where('d.size_id', $filter['s']);
        }

        if(isset($filter['d']) &&  $filter['d'] !='' ){
            $this->db->where('DATE(a.waktu_out) = \''.$filter['d'].'\'');
        }


        $hasil = $this->db->get()->result();
        $query = $this->db->last_query();    
     //   print_r($query);

//echo var_dump($hasil);
        return  $hasil;
    }


  function get_by_packing($id = NULL) {
        $this->db->select('a.*,b.*,c.*,c.item_number,d.order_detail_id,e.stone_color,f.no_shipment,f.tgl_export,mc.company_name,mc.customer_code');   
        $this->db->from('tb_packing_item a');    
        $this->db->join('tb_proses_order_detail b', 'b.id_proses_detail  = a.proses_order_detail_id', 'left');
        $this->db->join('tb_proses_order d', 'd.id_proses  = b.proses_order_id', 'left');
        $this->db->join('tb_order_detail e', 'e.id_order_detail  = d.order_detail_id', 'left');
        $this->db->join('master_item c', 'c.id_item  = b.item_id', 'left');
        $this->db->join('tb_packing f', 'f.id_packing  = a.packing_id', 'left');
        $this->db->join('master_customer mc', 'e.customer_id  = mc.id', 'left');
        $this->db->where('f.no_shipment', $id);
        $this->db->order_by('a.id_packing_item', 'DESC');
        return $this->db->get()->result();
    }

    function getbyPO($cancel=false) {
    $filter = $this->session->userdata('filter');
       if(isset($filter['d']) &&  $filter['d'] !='' ){
            $date = date('Y-m-d', strtotime($filter['d'] . ' +1 day')); 
            $where = 'WHERE input_date <  \''.$date.'\'';
        }else{
            $where = '';
        }
        $this->db->select('LEFT(a.line_no,LOCATE(\'-\',a.line_no) - 1) as noku,a.id_proses_detail,a.proses_order_id,a.no_po,a.line_no,a.waktu_proses,c.item_number,a.barcode,a.metal,a.qty,a.id_bagian_pengirim,a.id_bagian_asal,a.id_bagian_tujuan,a.wax_id,a.casting_id,a.status,a.waktu_in,a.waktu_out,a.in_weight,a.gold_part,a.gold_chain,a.other_part,a.stone_weight,a.gold_weight_in,a.out_weight,a.powder,a.nancip,a.potongan,a.gold_weight_out,a.loss,a.gold_replace,a.note_asal,a.note_tujuan,b.nama_bagian,a.in_weight,ba.nama_bagian as tujuan,g.melting_type_id,h.title_melting_type,to.size,i.nilai,f.*,to.stone_type,to.export_date,to.stone_qty,ms.conversi_value,ms.stone_name,mc.company_name,mc.customer_code,d.dia_carat,d.note as dia_note,j.pending_master,j.pending_part,j.pending_diamond,j.pending_other');
        $this->db->from('tb_proses_order_detail a');
        $this->db->join('master_bagian b', 'b.id_bagian=a.id_bagian_pengirim', 'left');
        $this->db->join('master_item c', 'c.id_item  = a.item_id', 'left');
        $this->db->join('tb_diamond d', 'd.barcode  = a.barcode', 'left');
        $this->db->join('master_bagian ba', 'ba.id_bagian=a.id_bagian_tujuan', 'left');
       // $this->db->join('tb_weight_proses f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');
        $this->db->join('tb_proses_order g', 'g.id_proses  = a.proses_order_id', 'left');
        $this->db->join('tb_order_detail to', 'to.id_order_detail  = g.order_detail_id', 'left');
        $this->db->join('master_customer mc', 'to.customer_id  = mc.id', 'left');
        $this->db->join('master_stone_type ms', 'ms.id  = to.stone_type', 'left');
        $this->db->join('master_melting_type h', 'h.id_melting_type  = g.melting_type_id', 'left');
        $this->db->join('master_size i', 'i.id  = h.size_id', 'left');
        $this->db->join('tb_pending j', 'j.barcode = a.barcode', 'left');
        $this->db->join('(SELECT
                    sum( gold_part ) AS out_gold_part,
                    sum( gold_chain ) AS out_gold_chain,
                    sum( other_part ) AS out_other_part,
                    sum( stone_weight ) AS out_stone_weight,
                    input_date,
                    proses_order_detail_id 
                FROM
                    tb_weight_proses '.$where.'  GROUP BY proses_order_detail_id 
               ) f', 'a.id_proses_detail = f.proses_order_detail_id', 'left');


        
        if($filter['f'] =='cancel'){
            $this->db->where('a.status !=','finish');
            $this->db->where('a.status =','cancel'); 
        }elseif ($filter['f'] =='proses') {
            $this->db->where('a.status !=','finish');
            $this->db->where('a.status !=','cancel');    
        }elseif ($filter['f'] =='reject') {
            $this->db->where('a.status !=','finish');
            $this->db->where('a.status =','reject');    
        }elseif ($filter['f'] =='close') {
            $this->db->where('a.status !=','finish');
            $this->db->where('a.status =','close');    
        }elseif ($filter['f'] =='sending') {
            $this->db->where('a.status !=','finish');
            $this->db->where('a.status !=','close');
            $this->db->where('a.status =','sending');    
        }else{
           $this->db->where('a.status !=','cancel'); 
           $this->db->where('a.status !=','finish');
        }

        // if($cancel){
        //  $this->db->where('a.status !=','cancel');           
        // }else{
        //      $this->db->where('a.status ==','cancel'); 
        // }
        if(isset($filter['po']) &&  $filter['po'] !='' ){
             $this->db->where('a.no_po', $filter['po']);
        }
        if(isset($filter['c']) &&  $filter['c'] !='' ){
             $this->db->where('mc.customer_code', strtoupper($filter['c']));
        }
        $this->db->group_by("a.id_proses_detail");
        $this->db->order_by("a.proses_order_id","ASC");
        $this->db->order_by("noku","ASC");

                $lquery = $this->db->last_query();    
    //  echo var_dump($filter,$lquery);

        return $this->db->get()->result();
    }



    function getbyPOproses() {
    $filter = $this->session->userdata('filter');
    $poid = array_filter(explode(PHP_EOL,$filter['poid']));
    $poid = implode("','",$poid);
    $poid = trim(str_replace(' ','',$poid));
    $poid = preg_replace('/\s+/', '', $poid);
    $idpacking = ID_PACKING;

    if(isset( $poid) &&   $poid !='' ){
         $powhere = " `od`.`no_po` IN ( '$poid' ) 
                AND ";
    }else{
        $powhere ='';
    }

     if(isset( $filter['c'] ) &&   $filter['c'] !='' ){
         $powhere .= " `odt`.`customer_code` ='".$filter['c']."'
                AND ";
    }

//echo var_dump($filter);

if($filter['p'] =='close'){
$fstatus = "WHERE `status` = 'close'  OR  `status` = 'cancel' " ;
}elseif ($filter['p'] =='cancel') {
  $fstatus = "WHERE (`status` = 'proses' OR  `status` = 'cancel') AND jumlah_cancel > 0 ";
}else{
$fstatus = "WHERE `status` = 'proses' OR  `status` = 'cancel'";
}
    $sql =" SELECT
            id_bagian_asal,
            no_po,
            sum( qty ) qty,
            first_name,
            last_name,
            company_name,
            jumlah_proses,
            jumlah_order,
            jumlah_close,
            jumlah_cancel,
            tgl_order,
            status,
            tgl_export FROM 
            (
            SELECT
                od.no_po,
                od.qty,
                od.id_bagian_asal,
                g.jumlah_proses,
                odt.jumlah_order,
                odc.jumlah_close,
                odca.jumlah_cancel,
                odt.tgl_order,
                odt.tgl_export,
                odt.first_name,
                odt.company_name,
                odt.customer_code,
                odt.last_name,
                od.STATUS AS status_asli,
            CASE
                    id_bagian_asal 
                    WHEN 4 THEN
                    ( IF ( `status` = 'proses' OR `status` = 'sending', 'proses', 'finish' ) ) 
                    WHEN $idpacking THEN
                    ( IF ( `status` = 'close' OR STATUS = 'finish', 'close', 'proses' ) ) 
                    ELSE ( IF ( `status` = 'cancel', 'cancel',IF(`status` != 'finish', 'proses', 'finish'  ) ) ) 
            END STATUS 
            FROM
                tb_proses_order_detail od
                LEFT JOIN ( SELECT id_proses, no_po, sum( qty ) AS jumlah_proses FROM tb_proses_order GROUP BY no_po ) g ON g.no_po = od.no_po
                LEFT JOIN ( SELECT tgl_order,customer_id,customer_code,first_name,last_name,export_date AS tgl_export, company_name,no_po, sum( qty ) AS jumlah_order FROM tb_order_detail  LEFT JOIN  master_customer mc ON mc.id = tb_order_detail.customer_id GROUP BY no_po ) odt ON odt.no_po = od.no_po
                LEFT JOIN ( SELECT no_po, sum( qty ) AS jumlah_close FROM tb_proses_order_detail WHERE `status` = 'close' AND id_bagian_asal = $idpacking GROUP BY no_po ) odc ON odc.no_po = od.no_po 
                LEFT JOIN ( SELECT no_po, sum( qty ) AS jumlah_cancel FROM tb_proses_order_detail WHERE `status` = 'cancel' GROUP BY no_po ) odca ON odca.no_po = od.no_po
            WHERE
              $powhere od.jenis = 'new' 
            -- GROUP BY
            --     od.barcode,
            --     od.id_bagian_asal 
            ) mumet $fstatus
        GROUP BY
            no_po,
            id_bagian_asal 
        ORDER BY tgl_export ASC";
     //  echo $sql;
        $query = $this->db->query($sql);
        $hasil = $query->result();
        $lquery = $this->db->last_query();    
      // echo var_dump($sql);
         return  $hasil;

    }


    function getbyPOproses_bak() {
    $filter = $this->session->userdata('filter');

    $array = array('casting','sending','reject','proses','repair','sending repair','proses repair','send cutting','cutting','request part'); //'casting','sending','proses','reject','finish','repair','cancel','receive','receive repair','close','sending repair','proses repair','finish repair','send cutting','cutting','rusak','recasting','refilling','rewax','request part'
    if($filter['p'] == 'close'){
        $array = array('close');
    }
    $proses = implode("','",$array);

    $poid = array_filter(explode(PHP_EOL,$filter['poid']));
    $poid = implode("','",$poid);
    $poid = trim(str_replace(' ','',$poid));
    $poid = preg_replace('/\s+/', '', $poid);
    $idpacking = ID_PACKING;
    $this->db->select('od.no_po,ot.production_date as production_date,ot.tgl_order as tgl_order,ot.export_date as export_date,od.id_bagian_asal,sum(od.qty) as jumlah,sum(g.qty) as jumlah_proses,ot.qty_order,oc.qty_close');
    $this->db->from('tb_proses_order_detail od');
    $this->db->join('tb_proses_order g', 'g.id_proses  = od.proses_order_id', 'left');
    $this->db->join('(SELECT
                sum( odt.qty ) AS qty_order,
                sum( odt.qty_proses ) AS qty_proses_last,odt.*
            FROM
                tb_order_detail odt GROUP BY odt.no_po ORDER By odt.no_po
           ) ot', 'ot.no_po  = g.no_po', 'left');

    $this->db->join("(SELECT no_po,
                sum( ocd.qty ) AS qty_close
            FROM
                tb_proses_order_detail ocd 
            WHERE ocd.status ='close' AND id_bagian_asal = $idpacking
            GROUP BY ocd.no_po ORDER By ocd.no_po
           ) oc", 'oc.no_po  = g.no_po', 'left');
       // $this->db->where("(od.status != 'finish' AND od.status != 'finish repair' AND od.status != 'close') ");   
        $this->db->where(" od.status IN ('".$proses."') ");
        $this->db->where("od.jenis = 'new'");
        if(isset( $poid) &&   $poid !='' ){
             $this->db->where(" od.no_po  IN ('".$poid."') ");
        }
        $this->db->group_by("od.no_po");
        $this->db->group_by("od.id_bagian_asal");
    //    $this->db->order_by("od.no_po","DESC");
        $this->db->order_by("od.line_no","DESC");
        $hasil = $this->db->get()->result();
        $lquery = $this->db->last_query();    
     //  echo var_dump($lquery);
        return $hasil;
    }

    function getbyPacking(){
	$this->db->select('a.*,b.stone_carat');
       $this->db->from('view_packing a');
        $this->db->join('view_diamond b', 'a.barcode  = b.barcode', 'left');
        $filter = $this->session->userdata('filter');
        if(isset($filter['no']) &&  $filter['no'] !='' ){
             $this->db->where('a.no_shipment', $filter['no']);
        }

     //$this->db->order_by('id_melting_type', 'ASC');
        return $this->db->get()->result();

    }

    function get_bagian()
    {
        
        $this->db->from('master_bagian');
        $this->db->where('proses','Y');
        $this->db->order_by('no', 'ASC');
       $b = $this->db->get()->result();
       $bagian = array();
      foreach ($b as $key => $item) {
        $bagian[$item->id_bagian] =  $item->nama_bagian;
      }
      return $bagian;

    }



  // get all
    function get_meltingall()
    {
        
        $this->db->from('master_melting_type a');
        $this->db->join('master_size c', 'c.id=a.size_id', 'left');
        $this->db->order_by('id_melting_type', 'ASC');
        return $this->db->get()->result();
    }


function getTrsAll($tanggal,$size){
    
  //  echo var_dump($tanggal,$size);
    $this->db->where('tanggal', $tanggal);
    $this->db->where('size', $size);
    $this->db->order_by('id_bagian_asal', 'ASC');
    return $this->db->get('view_loss')->result_array();
}

function getStocknow(){
    return $this->db->get('view_stock_now')->result();
}

function getMeltingnow(){
    return $this->db->get('view_melting_now')->result();
}

function getStockAll($tanggal1,$tanggal2){
    $tanggal2 = date('Y-m-d',strtotime($tanggal2 . "+1 days"));
    $this->db->where('tanggal >=', $tanggal1);
    $this->db->where('tanggal <=', $tanggal2);
    return $this->db->get('view_stock_saldo')->result_array();
}

function getMeltingAll($tanggal1,$tanggal2){
    $tanggal2 = date('Y-m-d',strtotime($tanggal2 . "+1 days"));
    $this->db->where('tanggal >=', $tanggal1);
    $this->db->where('tanggal <=', $tanggal2);
    return $this->db->get('view_melting_stock_saldo')->result_array();
}



    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }

    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('id_proses_detail', $q);
    $this->db->or_like('proses_order_id', $q);
    $this->db->or_like('no_po', $q);
    $this->db->or_like('line_no', $q);
    $this->db->or_like('waktu_proses', $q);
    $this->db->or_like('item_number', $q);
    $this->db->or_like('barcode', $q);
    $this->db->or_like('metal', $q);
    $this->db->or_like('qty', $q);
    $this->db->or_like('id_bagian_asal', $q);
    $this->db->or_like('id_bagian_tujuan', $q);
    $this->db->or_like('wax_id', $q);
    $this->db->or_like('casting_id', $q);
    $this->db->or_like('status', $q);
    $this->db->or_like('waktu_in', $q);
    $this->db->or_like('waktu_out', $q);
    $this->db->or_like('in_weight', $q);
    $this->db->or_like('gold_part', $q);
    $this->db->or_like('gold_chain', $q);
    $this->db->or_like('other_part', $q);
    $this->db->or_like('stone_weight', $q);
    $this->db->or_like('gold_weight_in', $q);
    $this->db->or_like('out_weight', $q);
    $this->db->or_like('powder', $q);
    $this->db->or_like('nancip', $q);
    $this->db->or_like('potongan', $q);
    $this->db->or_like('gold_weight_out', $q);
    $this->db->or_like('loss', $q);
    $this->db->or_like('gold_replace', $q);
    $this->db->or_like('note_asal', $q);
    $this->db->or_like('note_tujuan', $q);
    $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('id_proses_detail', $q);
    $this->db->or_like('proses_order_id', $q);
    $this->db->or_like('no_po', $q);
    $this->db->or_like('line_no', $q);
    $this->db->or_like('waktu_proses', $q);
    $this->db->or_like('item_number', $q);
    $this->db->or_like('barcode', $q);
    $this->db->or_like('metal', $q);
    $this->db->or_like('qty', $q);
    $this->db->or_like('id_bagian_asal', $q);
    $this->db->or_like('id_bagian_tujuan', $q);
    $this->db->or_like('wax_id', $q);
    $this->db->or_like('casting_id', $q);
    $this->db->or_like('status', $q);
    $this->db->or_like('waktu_in', $q);
    $this->db->or_like('waktu_out', $q);
    $this->db->or_like('in_weight', $q);
    $this->db->or_like('gold_part', $q);
    $this->db->or_like('gold_chain', $q);
    $this->db->or_like('other_part', $q);
    $this->db->or_like('stone_weight', $q);
    $this->db->or_like('gold_weight_in', $q);
    $this->db->or_like('out_weight', $q);
    $this->db->or_like('powder', $q);
    $this->db->or_like('nancip', $q);
    $this->db->or_like('potongan', $q);
    $this->db->or_like('gold_weight_out', $q);
    $this->db->or_like('loss', $q);
    $this->db->or_like('gold_replace', $q);
    $this->db->or_like('note_asal', $q);
    $this->db->or_like('note_tujuan', $q);
    $this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}

/* End of file Laporan_model.php */
/* Location: ./application/models/Laporan_model.php */
