import requests
import os
from openpyxl.styles import Alignment
from openpyxl import load_workbook
from datetime import datetime
# getcdidata, opencditemp, fillcdidata
def getcdrdata(date):
    # get data from http://qcstag.veronique.idn/cutting/report-in-items/2025-09-09?export=json
    url = f"http://qcstag.veronique.idn/api/cutting/cetak-laporan/{date}"
    params = {
        'output': 'json'
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        json_data = response.json()
        return json_data
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return None
    
def opencdrtemp():
    # open file /templates/cutting_dailyreport.xlsx
    template_path = os.path.join('templates', 'cutting_dayreport.xlsx')

    # load workbook
    wb = load_workbook(template_path)
    return wb
def fillcdrdata(wb, date):
    output_path = os.path.join('static', 'cutting_dailyreport.xlsx')
    # remove output_path if exist
    if os.path.exists(output_path):
        os.remove(output_path)
    data = getcdrdata(date)
    sheet = wb.active
    # Alignment configuration
    ca = Alignment(horizontal='center', vertical='center')
    la = Alignment(horizontal='left', vertical='center')
    ra = Alignment(horizontal='right', vertical='center')

    # set today to Cell B4
    sheet['B4'] = date

    # Column indexes = Thong,Metal Color,Sisa Yg Lalu,Pohon In,Qty,Pohon Out,Out Stripping,Out Recasting,Potongan,Item Out,Loss,Sisa 
    # start from row 7
    row_num = 7

    # total init for each column
    t_pohon_in = 0
    t_pohon_out = 0
    t_quantity = 0
    t_out_stripp =0
    t_out_recast = 0
    t_potongan = 0
    t_item_out = 0
    t_loss = 0
    t_sisa = 0

    # Loop all data
    for row in data:
        sheet[f'A{row_num}'] = row.get('thong', '')
        sheet[f'A{row_num}'].alignment = ca
        sheet[f'B{row_num}'] = row.get('metal_color', '')
        sheet[f'B{row_num}'].alignment = ca
        sheet[f'C{row_num}'] = row.get('sisa_lalu','')
        sheet[f'C{row_num}'].alignment = ca
        sheet[f'D{row_num}'] = row.get('pohon_in',0)
        sheet[f'D{row_num}'].alignment = ra
        sheet[f'E{row_num}'] = row.get('qty',0)
        sheet[f'E{row_num}'].alignment = ra
        sheet[f'F{row_num}'].value = row.get('pohon_out',0)
        sheet[f'F{row_num}'].alignment = ra
        sheet[f'G{row_num}'] = row.get('out_stripping',0)
        sheet[f'G{row_num}'].alignment = ra
        sheet[f'H{row_num}'] = row.get('out_recasting',0)
        sheet[f'H{row_num}'].alignment = ra
        sheet[f'I{row_num}'] = row.get('potongan',0)
        sheet[f'I{row_num}'].alignment = ra
        sheet[f'J{row_num}']= row.get('item_out',0)
        sheet[f'J{row_num}'].alignment = ra
        sheet[f'K{row_num}']= row.get('loss',0)
        sheet[f'K{row_num}'].alignment = ra
        sheet[f'L{row_num}']= row.get('sisa',0)
        sheet[f'L{row_num}'].alignment = ca
        
        t_pohon_in += row.get('pohon_in', 0)
        t_pohon_out += row.get('pohon_out', 0)
        t_quantity += row.get('qty', 0)
        t_out_stripp += row.get('out_stripping', 0)
        t_out_recast += row.get('out_recasting', 0)
        t_potongan += row.get('potongan', 0)
        t_item_out += row.get('item_out', 0)
        t_loss += row.get('loss', 0)
        t_sisa += row.get('sisa', 0)
        row_num += 1

    # Fill total weight
    sheet[f'D{row_num}'] = t_pohon_in
    sheet[f'D{row_num}'].alignment = ra
    sheet[f'E{row_num}'] = t_quantity
    sheet[f'E{row_num}'].alignment = ra
    sheet[f'F{row_num}'] = t_pohon_out
    sheet[f'F{row_num}'].alignment = ra
    sheet[f'G{row_num}'] = t_out_stripp
    sheet[f'G{row_num}'].alignment = ra
    sheet[f'H{row_num}'] = t_out_recast
    sheet[f'H{row_num}'].alignment = ra
    sheet[f'I{row_num}'] = t_potongan
    sheet[f'I{row_num}'].alignment = ra
    sheet[f'J{row_num}'] = t_item_out
    sheet[f'J{row_num}'].alignment = ra
    sheet[f'K{row_num}'] = t_loss
    sheet[f'K{row_num}'].alignment = ra
    sheet[f'L{row_num}'] = t_sisa
    sheet[f'L{row_num}'].alignment = ra
    
    wb.save(output_path)
    return output_path



        
