import mariadb
import sys

def db_connect():
    try:
        conn = mariadb.connect(
            user="invero",
            password="invero",
            host="***********",
            port=3306,
            database="gold2025"
        )
    except mariadb.Error as e:
        print(f"Error connecting to MariaDB Platform: {e}")
        sys.exit(1)
    return conn

def rpsdata(date):
    query =  """
    SELECT CAST(LEFT(a.line_no,LOCATE('-',a.line_no) - 1)  as UNSIGNED) as noka,
    CAST(RIGHT(a.line_no, LENGTH(a.line_no) - LOCATE('-',a.line_no))  as UNSIGNED) as nokb,
    a.id_proses_detail, a.proses_order_id, a.no_po, a.line_no, a.waktu_proses, a.barcode, a.metal, a.qty, a.id_bagian_asal, a.id_bagian_tujuan, a.wax_id, a.casting_id, a.status, a.waktu_in, a.waktu_out, a.in_weight, a.gold_part, a.gold_chain, a.other_part, a.stone_weight, a.gold_weight_in, a.out_weight, a.powder, a.nancip, a.potongan, a.gold_weight_out, a.loss, a.gold_replace, a.note_asal, a.note_tujuan, b.nama_bagian, c.nama_bagian as pengirim, d.nama_bagian as tujuan, g.melting_type_id, h.title_melting_type, i.size, i.nilai, f.* FROM tb_proses_order_detail a
    LEFT JOIN master_bagian b ON b.id_bagian = a.id_bagian_asal
    LEFT JOIN master_bagian c ON c.id_bagian = a.id_bagian_pengirim
    LEFT JOIN master_bagian d ON d.id_bagian = a.id_bagian_tujuan
    LEFT JOIN tb_diamond dm ON dm.barcode  = a.barcode
    LEFT JOIN tb_proses_order g ON g.id_proses  = a.proses_order_id
    LEFT JOIN tb_order_detail to ON to.id_order_detail  = g.order_detail_id
    LEFT JOIN master_stone_type ms ON ms.id  = to.stone_type
    LEFT JOIN master_melting_type h ON h.id_melting_type  = g.melting_type_id
    LEFT JOIN master_size i ON i.id  = h.size_id
    LEFT JOIN (SELECT
                    sum( gold_part ) AS out_gold_part,
                    sum( gold_chain ) AS out_gold_chain,
                    sum( other_part ) AS out_other_part,
                    sum( stone_weight ) AS out_stone_weight,
                    input_date,
                    proses_order_detail_id
                FROM
                    tb_weight_proses
                WHERE
                    input_date < ?
                GROUP BY
                    proses_order_detail_id
                ) f ON a.id_proses_detail = f.proses_order_detail_id
                WHERE
                    a.id_bagian_asal = 7
                    AND (a.status != 'finish' AND a.status != 'finish repair' AND a.status != 'close' AND a.status != 'casting' AND a.status != 'cutting' AND a.status != 'send cutting' AND a.status != 'send casting')
            GROUP BY
                a.id_proses_detail
            ORDER BY
                noka, nokb
            """
    conn = db_connect()
    cur = conn.cursor()
    try:
        cur.execute(query, (date,))
        result = cur.fetchall()
        conn.close()
        return result
    except mariadb.Error as e:
        print(f"Error: {e}")
        return None
