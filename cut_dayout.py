import requests
import os
from openpyxl.styles import Alignment
from openpyxl import load_workbook
from datetime import datetime
# getcdidata, opencditemp, fillcdidata
def getcdodata(date):
    # get data from http://qcstag.veronique.idn/cutting/report-in-items/2025-09-09?export=json
    url = f"http://qcstag.veronique.idn/cutting/report-out-items/{date}?export=json"
    params = {
        'output': 'json'
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        json_data = response.json()
        return json_data
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return None
    
def opencdotemp():
    # open file /templates/cut_dayin.xlsx
    template_path = os.path.join('templates', 'cut_dayout.xlsx')

    # load workbook
    wb = load_workbook(template_path)
    return wb
def fillcdodata(wb, date):
    output_path = os.path.join('templates', 'cutting_dailyout.xlsx')
    # remove output_path if exist
    if os.path.exists(output_path):
        os.remove(output_path)
    data = getcdodata(date)
    sheet = wb.active
    # Alignment configuration
    ca = Alignment(horizontal='center', vertical='center')
    la = Alignment(horizontal='left', vertical='center')
    ra = Alignment(horizontal='right', vertical='center')

    # set today to Cell C1
    sheet['C1'] = date

    # Column indexes = Category	PO NO	LINE #	ITEM NUMBER	BARCODE	QTY	METAL	WEIGHT	ONLY GOLD	GOLD 24K	TGL/JAM	PENGIRIM	STATUS	BAHAN BAKU
    # start from row 4
    row_num = 4

    # weight init
    twgt = 0;
    tqty = 0;

    # Loop all data
    for row in data:
        sheet[f'A{row_num}'] = row.get('category', '')
        sheet[f'A{row_num}'].alignment = ca
        # split row.get('barcode') by . 
        barcode = row.get('barcode', '')
        split_barcode = barcode.split('.')
        sheet[f'B{row_num}'] = split_barcode[0]
        sheet[f'B{row_num}'].alignment = la
        sheet[f'C{row_num}'] = split_barcode[2]
        sheet[f'C{row_num}'].alignment = ca
        sheet[f'D{row_num}'] = row.get('item_name','')
        sheet[f'D{row_num}'].alignment = la
        sheet[f'E{row_num}'] = row.get('barcode','')
        sheet[f'E{row_num}'].alignment = la
        iqty = str(row.get('qty',''))
        sheet[f'F{row_num}'].value = iqty
        sheet[f'F{row_num}'].alignment = ra
        split_cuttingbc = row.get('cuttingbc', '').split('-')
        metal_color = f"{split_cuttingbc[0]}-{split_cuttingbc[1]}"
        sheet[f'G{row_num}'] = metal_color
        sheet[f'G{row_num}'].alignment = ca
        strwgt = row.get('weight','0')
        try:
            flowgt = float(strwgt)
        except ValueError:
            flowgt = 0.0
        sheet[f'H{row_num}'] = flowgt
        sheet[f'H{row_num}'].number_format ="0.00"
        sheet[f'H{row_num}'].alignment = ra
        sheet[f'I{row_num}'] = ''
        sheet[f'J{row_num}']=''
        sheet[f'K{row_num}']= row.get('created_at','')
        sheet[f'K{row_num}'].alignment = ra
        sheet[f'L{row_num}']='Cutting 1'
        sheet[f'L{row_num}'].alignment = ca
        sheet[f'M{row_num}']=''
        sheet[f'N{row_num}']=''
        
        tqty = tqty + int(row.get('qty', 0))
        twgt += float(row.get('weight', 0))
        row_num += 1

    # Fill total weight
    sheet[f'F{row_num}'] = tqty
    sheet[f'F{row_num}'].alignment = ra
    sheet[f'H{row_num}'] = twgt
    sheet[f'H{row_num}'].alignment = ra
    wb.save(output_path)
    return output_path



        
