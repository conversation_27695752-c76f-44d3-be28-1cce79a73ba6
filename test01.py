from flask import Flask, render_template, send_file
import pandas as pd
import openpyxl
import os
import requests
from datetime import datetime


def getreport(date):
    url = "http://localhost:8104/melting/daily-report"
    params = {
        'date': date,
        'output': 'json'
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        json_data = response.json()
        return json_data
        

    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")

app = Flask(__name__)

SOURCE_FILE = 'dailyTemplate.xlsx'

@app.route('/')
def home():
    return render_template("index.html")

@app.route('/report/<date>')
def download(date):
    # delete file dailyreport.xlsx if any
    if os.path.exists('dailyReport.xlsx'):
        os.remove('dailyReport.xlsx')
    #copy dailytemplate
    new_file = openpyxl.load_workbook(SOURCE_FILE)
    sheet = new_file.active
    current_date = datetime.now().strftime("%d/%m/%Y")
    # fill cell B2 with current date
    # sheet.cell(row=2, column=2).value = current_date
    sheet.cell(row=2, column=2).value = date
    
    NOMOR = 1
    data = getreport(date)
    if isinstance(data, list):
        df = pd.DataFrame(data)
        # print(df)
        METAL = df['metal'].tolist()
        COLOR = df['color'].tolist()
        ORIGINAL = df['original_weight'].tolist()
        ALLOY = df['alloy_weight'].tolist()
        POTONGAN = df['potongan_weight'].tolist()
        POHON = df['pohon_weight'].tolist()
        IN_JUJO = df['melting_weight'].tolist()
        OUT_JUJO = df['box_weight'].tolist()
        GRANULE = df['granule_weight'].tolist()
        LOSS = (df['loss_weight']/100).tolist()
        L24K = df['k24k_weight'].tolist()
        KADAR_KARAT = df['kadarkarat'].tolist()
        t_ori = 0
        t_all = 0
        t_poh = 0
        t_pot = 0
        t_inj = 0
        t_ouj = 0
        t_gra = 0
        t_los = 0
        t_24k = 0
        t_24g = 0
        for i in range(len(METAL)):
            sheet.cell(row=i+4, column=1).value = NOMOR
            sheet.cell(row=i+4, column=2).value = METAL[i]
            sheet.cell(row=i+4, column=3).value = COLOR[i]
            sheet.cell(row=i+4, column=4).value = ORIGINAL[i]
            sheet.cell(row=i+4, column=5).value = ALLOY[i]
            sheet.cell(row=i+4, column=6).value = POHON[i]
            sheet.cell(row=i+4, column=7).value = POTONGAN[i]
            sheet.cell(row=i+4, column=8).value = IN_JUJO[i]
            sheet.cell(row=i+4, column=9).value = OUT_JUJO[i]
            sheet.cell(row=i+4, column=10).value = GRANULE[i]
            sheet.cell(row=i+4, column=11).value = LOSS[i]
            sheet.cell(row=i+4, column=12).value = L24K[i]
            G24K = (GRANULE[i] * KADAR_KARAT[i])
            LOSS_RATE = (LOSS[i]/IN_JUJO[i] * 100)
            sheet.cell(row=i+4, column=13).value = G24K
            sheet.cell(row=i+4, column=14).value = LOSS_RATE
            sheet.cell(row=i+4, column=15).value = KADAR_KARAT[i]
            NOMOR += 1
        # Total data
        sheet.cell(row=len(METAL)+4, column=2).value = 'Total'
        sheet.cell(row=len(METAL)+4, column=4).value = t_ori
        sheet.cell(row=len(METAL)+4, column=5).value = t_all
        sheet.cell(row=len(METAL)+4, column=6).value = t_poh
        sheet.cell(row=len(METAL)+4, column=7).value = t_pot
        sheet.cell(row=len(METAL)+4, column=8).value = t_inj
        sheet.cell(row=len(METAL)+4, column=9).value = t_ouj
        sheet.cell(row=len(METAL)+4, column=10).value = t_gra
        sheet.cell(row=len(METAL)+4, column=11).value = t_los
        sheet.cell(row=len(METAL)+4, column=12).value = t_24k
        sheet.cell(row=len(METAL)+4, column=13).value = t_24g
        sheet.cell(row=len(METAL)+4, column=14).value = (t_los/t_inj * 100)
        # Total data plus granule
        sheet.cell(row=len(METAL)+5, column=2).value = 'Total + Granule'
        sheet.cell(row=len(METAL)+5, column=4).value = t_ori
        sheet.cell(row=len(METAL)+5, column=5).value = t_all
        sheet.cell(row=len(METAL)+5, column=6).value = t_poh
        sheet.cell(row=len(METAL)+5, column=7).value = t_pot
        sheet.cell(row=len(METAL)+5, column=8).value = t_inj
        sheet.cell(row=len(METAL)+5, column=9).value = t_ouj
        sheet.cell(row=len(METAL)+5, column=10).value = t_gra
        sheet.cell(row=len(METAL)+5, column=11).value = t_los
        sheet.cell(row=len(METAL)+5, column=12).value = t_24k
        sheet.cell(row=len(METAL)+5, column=13).value = t_24g
        sheet.cell(row=len(METAL)+5, column=14).value = ((t_inj - (t_ouj + t_gra))/t_inj * 100)
        new_file.save('dailyReport.xlsx')

        return send_file('dailyReport.xlsx', as_attachment=True)
    else:
        return "Unexpected JSON format. Expected a list of dictionaries.", 500


@app.route('/test')
def test():
    data = getreport('2024-12-27')
    if isinstance(data, list):
        df = pd.DataFrame(data)
        print(df)
    else:
        return "Unexpected JSON format. Expected a list of dictionaries.", 500
    return render_template("index.html")
    
    

if __name__ == '__main__':
    app.run(debug=True)