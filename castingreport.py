# report.py
from flask import request, jsonify
import json
# import openpyxl
from openpyxl.styles import Alignment
from openpyxl import load_workbook
import shutil
import pandas as pd
import os

def report():
    # Delete dailyreport.xlsx
    delfi = delete_file() 

    try:
        if request.is_json:
            json_data = request.get_json()
        else:
            json_data = json.loads(request.form.get('data'))
        
        drep_data = json_data.get('dayreport',{}).get('data',{})
        drep_wogd = json_data.get('dayreport',{}).get('wog',{})
        drep_wigd = json_data.get('dayreport',{}).get('wig',{})

        dres_data = json_data.get('dayresume',{}).get('data',{})
        dres_wogd = json_data.get('dayresume',{}).get('wog',{})
        dres_wigd = json_data.get('dayresume',{}).get('wig',{})
        # Put drep to templates/dailyreport.xlsx sheet LAPORAN1 start from row 11
        # Define the path to the Excel template
        template_path = os.path.join('templates', 'casting.xlsx')
        output_path = os.path.join('templates', 'dailyreport.xlsx')
        
        # Load the workbook
        wb = load_workbook(template_path)
        
        # Select the LAPORAN1 sheet
        sheet = wb['LAPORAN1']
        
        # Start writing data from row 11
        row_num = 10
        urut = 1
        # Alignment configuration
        ca = Alignment(horizontal='center', vertical='center')
        la = Alignment(horizontal='left', vertical='center')
        ra = Alignment(horizontal='right', vertical='center')
        for row in drep_data:
            sheet[f'A{row_num}'] = urut
            sheet[f'A{row_num}'].alignment = ca
            sheet[f'B{row_num}'] = row.get('metal', '')
            sheet[f'B{row_num}'].alignment = ca
            sheet[f'C{row_num}'] = row.get('color', '')
            sheet[f'C{row_num}'].alignment = ca
            sheet[f'D{row_num}'] = row.get('no_pohon', '')
            sheet[f'D{row_num}'].alignment = ca
            sheet[f'E{row_num}'] = row.get('casting_in', '')
            sheet[f'E{row_num}'].alignment = ra
            sheet[f'F{row_num}'] = row.get('casting_out', '')
            sheet[f'F{row_num}'].alignment = ra
            sheet[f'G{row_num}'] = row.get('casting_loss', '')
            sheet[f'G{row_num}'].alignment = ra
            sheet[f'H{row_num}'] = row.get('casting_24k', '')
            sheet[f'H{row_num}'].alignment = ra
            sheet[f'I{row_num}'] = row.get('casting_rate', '')
            sheet[f'I{row_num}'].alignment = ra
            sheet[f'J{row_num}'] = row.get('granule', '')
            sheet[f'J{row_num}'].alignment = ra
            sheet[f'K{row_num}'] = row.get('gw24k', '')
            sheet[f'K{row_num}'].alignment = ra
            sheet[f'L{row_num}'] = row.get('stripping_in', '')
            sheet[f'L{row_num}'].alignment = ra
            sheet[f'M{row_num}'] = row.get('stripping_out', '')
            sheet[f'M{row_num}'].alignment = ra
            sheet[f'N{row_num}'] = row.get('stripping_loss', '')
            sheet[f'N{row_num}'].alignment = ra
            sheet[f'O{row_num}'] = row.get('stripping_24k', '')
            sheet[f'O{row_num}'].alignment = ra
            sheet[f'P{row_num}'] = row.get('stripping_rate', '')
            sheet[f'P{row_num}'].alignment = ra
            row_num += 1
            urut+=1
        # Total without granule
        sheet[f'A{row_num}'] = "Total"
        sheet[f'A{row_num}'].alignment = la
        sheet[f'E{row_num}'] = drep_wogd.get('casting_in', '')
        sheet[f'E{row_num}'].alignment = ra
        sheet[f'F{row_num}'] = drep_wogd.get('casting_out', '')
        sheet[f'F{row_num}'].alignment = ra
        sheet[f'G{row_num}'] = drep_wogd.get('casting_loss', '')
        sheet[f'G{row_num}'].alignment = ra
        sheet[f'H{row_num}'] = drep_wogd.get('casting_24k', '')
        sheet[f'H{row_num}'].alignment = ra
        sheet[f'I{row_num}'] = drep_wogd.get('casting_rate', '')
        sheet[f'I{row_num}'].alignment = ra
        sheet[f'J{row_num}'] = drep_wogd.get('granule', '')
        sheet[f'J{row_num}'].alignment = ra
        sheet[f'K{row_num}'] = drep_wogd.get('gw24k', '')
        sheet[f'K{row_num}'].alignment = ra
        sheet[f'L{row_num}'] = drep_wogd.get('stripping_in', '')
        sheet[f'L{row_num}'].alignment = ra
        sheet[f'M{row_num}'] = drep_wogd.get('stripping_out', '')
        sheet[f'M{row_num}'].alignment = ra
        sheet[f'N{row_num}'] = drep_wogd.get('stripping_loss', '')
        sheet[f'N{row_num}'].alignment = ra
        sheet[f'O{row_num}'] = drep_wogd.get('stripping_24k', '')
        sheet[f'O{row_num}'].alignment = ra
        sheet[f'P{row_num}'] = drep_wogd.get('stripping_rate', '')
        sheet[f'P{row_num}'].alignment = ra
        row_num += 1
        # Total with granule
        sheet[f'A{row_num}'] = "Total with Granule"
        sheet[f'A{row_num}'].alignment = la
        sheet[f'E{row_num}'] = drep_wigd.get('casting_in', '')
        sheet[f'E{row_num}'].alignment = ra
        sheet[f'F{row_num}'] = drep_wigd.get('casting_out', '')
        sheet[f'F{row_num}'].alignment = ra
        sheet[f'G{row_num}'] = drep_wigd.get('casting_loss', '')
        sheet[f'G{row_num}'].alignment = ra
        sheet[f'H{row_num}'] = drep_wigd.get('casting_24k', '')
        sheet[f'H{row_num}'].alignment = ra
        sheet[f'I{row_num}'] = drep_wigd.get('casting_rate', '')
        sheet[f'I{row_num}'].alignment = ra
        sheet[f'J{row_num}'] = drep_wigd.get('granule', '')
        sheet[f'J{row_num}'].alignment = ra
        sheet[f'K{row_num}'] = drep_wigd.get('gw24k', '')
        sheet[f'K{row_num}'].alignment = ra
        sheet[f'L{row_num}'] = drep_wigd.get('stripping_in', '')
        sheet[f'L{row_num}'].alignment = ra
        sheet[f'M{row_num}'] = drep_wigd.get('stripping_out', '')
        sheet[f'M{row_num}'].alignment = ra
        sheet[f'N{row_num}'] = drep_wigd.get('stripping_loss', '')
        sheet[f'N{row_num}'].alignment = ra
        sheet[f'O{row_num}'] = drep_wigd.get('stripping_24k', '')
        sheet[f'O{row_num}'].alignment = ra
        sheet[f'P{row_num}'] = drep_wigd.get('stripping_rate', '')
        sheet[f'P{row_num}'].alignment = ra
        
        # Fill Sheet LAPORAN 2 
        sheet = wb['LAPORAN2']
        
        # Start writing data from row 11
        row_num = 10
        urut = 1

        for row in dres_data:
            sheet[f'A{row_num}'] = row.get('metal_color', '')
            sheet[f'A{row_num}'].alignment = ca
            sheet[f'B{row_num}'] = row.get('casting_in', '')
            sheet[f'B{row_num}'].alignment = ra
            sheet[f'C{row_num}'] = row.get('casting_out', '')
            sheet[f'C{row_num}'].alignment = ra
            sheet[f'D{row_num}'] = row.get('casting_loss', '')
            sheet[f'D{row_num}'].alignment = ra
            sheet[f'E{row_num}'] = row.get('casting_24k', '')
            sheet[f'E{row_num}'].alignment = ra
            sheet[f'F{row_num}'] = row.get('casting_rate', '')
            sheet[f'F{row_num}'].alignment = ra
            sheet[f'G{row_num}'] = row.get('granule', '')
            sheet[f'G{row_num}'].alignment = ra
            sheet[f'H{row_num}'] = row.get('gw24k', '')
            sheet[f'H{row_num}'].alignment = ra
            sheet[f'I{row_num}'] = row.get('stripping_in', '')
            sheet[f'I{row_num}'].alignment = ra
            sheet[f'J{row_num}'] = row.get('stripping_out', '')
            sheet[f'J{row_num}'].alignment = ra
            sheet[f'K{row_num}'] = row.get('stripping_loss', '')
            sheet[f'K{row_num}'].alignment = ra
            sheet[f'L{row_num}'] = row.get('stripping_24k', '')
            sheet[f'L{row_num}'].alignment = ra
            sheet[f'M{row_num}'] = row.get('stripping_rate', '')
            sheet[f'M{row_num}'].alignment = ra
            row_num += 1
        # Fill Total without granule
        sheet[f'A{row_num}'] = "Total without Granule"
        sheet[f'A{row_num}'].alignment = ca
        sheet[f'B{row_num}'] = drep_wogd.get('casting_in', '')
        sheet[f'B{row_num}'].alignment = ra
        sheet[f'C{row_num}'] = drep_wogd.get('casting_out', '')
        sheet[f'C{row_num}'].alignment = ra
        sheet[f'D{row_num}'] = drep_wogd.get('casting_loss', '')
        sheet[f'D{row_num}'].alignment = ra
        sheet[f'E{row_num}'] = drep_wogd.get('casting_24k', '')
        sheet[f'E{row_num}'].alignment = ra
        sheet[f'F{row_num}'] = drep_wogd.get('casting_rate', '')
        sheet[f'F{row_num}'].alignment = ra
        sheet[f'G{row_num}'] = drep_wogd.get('granule', '')
        sheet[f'G{row_num}'].alignment = ra
        sheet[f'H{row_num}'] = drep_wogd.get('gw24k', '')
        sheet[f'H{row_num}'].alignment = ra
        sheet[f'I{row_num}'] = drep_wogd.get('stripping_in', '')
        sheet[f'I{row_num}'].alignment = ra
        sheet[f'J{row_num}'] = drep_wogd.get('stripping_out', '')
        sheet[f'J{row_num}'].alignment = ra
        sheet[f'K{row_num}'] = drep_wogd.get('stripping_loss', '')
        sheet[f'K{row_num}'].alignment = ra
        sheet[f'L{row_num}'] = drep_wogd.get('stripping_24k', '')
        sheet[f'L{row_num}'].alignment = ra
        sheet[f'M{row_num}'] = drep_wogd.get('stripping_rate', '')
        sheet[f'M{row_num}'].alignment = ra
        row_num += 1
        # Fill Total with granule
        sheet[f'A{row_num}'] = "Total without Granule"
        sheet[f'A{row_num}'].alignment = ca
        sheet[f'B{row_num}'] = drep_wigd.get('casting_in', '')
        sheet[f'B{row_num}'].alignment = ra
        sheet[f'C{row_num}'] = drep_wigd.get('casting_out', '')
        sheet[f'C{row_num}'].alignment = ra
        sheet[f'D{row_num}'] = drep_wigd.get('casting_loss', '')
        sheet[f'D{row_num}'].alignment = ra
        sheet[f'E{row_num}'] = drep_wigd.get('casting_24k', '')
        sheet[f'E{row_num}'].alignment = ra
        sheet[f'F{row_num}'] = drep_wigd.get('casting_rate', '')
        sheet[f'F{row_num}'].alignment = ra
        sheet[f'G{row_num}'] = drep_wigd.get('granule', '')
        sheet[f'G{row_num}'].alignment = ra
        sheet[f'H{row_num}'] = drep_wigd.get('gw24k', '')
        sheet[f'H{row_num}'].alignment = ra
        sheet[f'I{row_num}'] = drep_wigd.get('stripping_in', '')
        sheet[f'I{row_num}'].alignment = ra
        sheet[f'J{row_num}'] = drep_wigd.get('stripping_out', '')
        sheet[f'J{row_num}'].alignment = ra
        sheet[f'K{row_num}'] = drep_wigd.get('stripping_loss', '')
        sheet[f'K{row_num}'].alignment = ra
        sheet[f'L{row_num}'] = drep_wigd.get('stripping_24k', '')
        sheet[f'L{row_num}'].alignment = ra
        sheet[f'M{row_num}'] = drep_wigd.get('stripping_rate', '')
        sheet[f'M{row_num}'].alignment = ra
        # Save the workbook
        wb.save(output_path)

        linkdl = "http://localhost:5000/templates/dailyreport.xlsx";


        return jsonify({"status": "success", "message": "Report generated successfully", "link": linkdl}), 200

    except json.JSONDecodeError as e:
        return jsonify({"status": "error1", "message": f"Invalid JSON: {str(e)}"}), 400
    except KeyError as e:
        return jsonify({"status": "error2", "message": f"Missing key in data: {str(e)}"}), 400
    except Exception as e:
        return jsonify({"status": "error3", "message": str(e)}), 500


def delete_file():
    try:
        os.remove('templates/dailyreport.xlsx')
        return jsonify({"status": "success", "message": "File deleted successfully"}), 200
    except FileNotFoundError:
        return jsonify({"status": "error", "message": "File not found"}), 404    


# def copyfile():
#     # Source file is templates/casting.xlsx
#     src = 'templates/casting.xlsx'
#     dst = 'templates/dailyreport.xlsx'
#     shutil.copy2(src, dst)
#     return("File Copied")
#     # Destination file is dailyCastingReport.xlsx
